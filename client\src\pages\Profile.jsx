import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FaEdit, FaCamera, FaSave, FaTimes, FaStar, FaTrophy, 
  FaFire, FaEye, FaHeart, FaUsers, FaCalendar, FaArrowLeft
} from 'react-icons/fa';
import { useTheme } from '../context/ThemeContext';
import { useNavigate } from 'react-router-dom';

const Profile = () => {
  const { isNightMode } = useTheme();
  const navigate = useNavigate();
  
  // Mock user data - will come from backend
  const [user, setUser] = useState({
    username: 'AnimeOtaku_2024',
    clan: '<PERSON>ru<PERSON>',
    rank: 'Jonin',
    level: 42,
    bio: 'Passionate anime fan who loves exploring new worlds and connecting with fellow otakus! 🌸',
    avatar: '🎭',
    joinDate: '2024-01-15',
    animeWatched: 89,
    animeCompleted: 89,
    currentlyWatching: [
      'Demon Slayer: <PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON> S3',
      'One Piece'
    ],
    planToWatch: 156,
    followers: 324,
    following: 198,
    posts: 67
  });

  // Mock anime lists
  const [animeWatchedList] = useState([
    'Naruto', 'Naruto Shippuden', 'Attack on Titan', 'Death Note', 'One Piece',
    'Demon Slayer', 'My Hero Academia', 'Jujutsu Kaisen', 'Tokyo Ghoul', 'Bleach',
    'Dragon Ball Z', 'Fullmetal Alchemist', 'Hunter x Hunter', 'Mob Psycho 100',
    'One Punch Man', 'Chainsaw Man', 'Spy x Family', 'Kimetsu no Yaiba'
  ]);

  const [planToWatchList] = useState([
    'Vinland Saga', 'Monster', 'Steins;Gate', 'Code Geass', 'Cowboy Bebop',
    'Neon Genesis Evangelion', 'Berserk', 'JoJo\'s Bizarre Adventure', 'Parasyte',
    'Tokyo Revengers', 'Fire Force', 'Dr. Stone', 'Promised Neverland'
  ]);

  const [completedList] = useState([
    'Death Note', 'Attack on Titan', 'Demon Slayer', 'Tokyo Ghoul',
    'Fullmetal Alchemist: Brotherhood', 'Hunter x Hunter', 'Mob Psycho 100',
    'One Punch Man', 'Chainsaw Man', 'Spy x Family'
  ]);

  const [isEditingBio, setIsEditingBio] = useState(false);
  const [tempBio, setTempBio] = useState(user.bio);
  const [isEditingAvatar, setIsEditingAvatar] = useState(false);
  const [activeListModal, setActiveListModal] = useState(null); // 'watched', 'completed', 'watching', 'planToWatch'

  // Clan-specific profile titles and colors
  const clanProfiles = {
    'Naruto': {
      ranks: ['Genin', 'Chunin', 'Jonin', 'Kage'],
      colors: { primary: 'orange-500', secondary: 'blue-500' },
      title: 'Ninja Profile',
      icon: '🍥'
    },
    'One Piece': {
      ranks: ['Cabin Boy', 'Pirate', 'Captain', 'Admiral'],
      colors: { primary: 'blue-600', secondary: 'red-500' },
      title: 'Pirate Profile',
      icon: '🏴‍☠️'
    },
    'Attack on Titan': {
      ranks: ['Cadet', 'Soldier', 'Squad Leader', 'Commander'],
      colors: { primary: 'green-600', secondary: 'brown-500' },
      title: 'Scout Profile',
      icon: '⚔️'
    },
    'Demon Slayer': {
      ranks: ['Mizunoto', 'Kinoe', 'Hashira', 'Master'],
      colors: { primary: 'purple-600', secondary: 'pink-500' },
      title: 'Slayer Profile',
      icon: '🗡️'
    },
    'My Hero Academia': {
      ranks: ['Student', 'Hero', 'Pro Hero', 'Symbol of Peace'],
      colors: { primary: 'green-500', secondary: 'yellow-500' },
      title: 'Hero Profile',
      icon: '💥'
    }
  };

  const currentClan = clanProfiles[user.clan] || clanProfiles['Naruto'];

  const handleSaveBio = () => {
    setUser({ ...user, bio: tempBio });
    setIsEditingBio(false);
  };

  const handleCancelBio = () => {
    setTempBio(user.bio);
    setIsEditingBio(false);
  };

  const avatarOptions = ['🎭', '🎨', '⚡', '🔥', '🌸', '🗡️', '🏴‍☠️', '💥', '👑', '🦋'];

  const achievements = [
    { name: 'First Watch', description: 'Completed first anime', icon: '🌟', earned: true, level: 'gold' },
    { name: 'Binge Master', description: 'Watched 24 episodes in one day', icon: '🔥', earned: true, level: 'silver' },
    { name: 'Clan Loyalty', description: 'Member for 6+ months', icon: '⚔️', earned: true, level: 'gold' },
    { name: 'Social Butterfly', description: '100+ followers', icon: '🦋', earned: true, level: 'silver' },
    { name: 'Review Master', description: 'Written 50+ reviews', icon: '📝', earned: true, level: 'copper' },
    { name: 'Event Organizer', description: 'Hosted community event', icon: '🎪', earned: false, level: 'gold' },
    { name: 'Anime Explorer', description: 'Watched 50+ different anime', icon: '🗺️', earned: true, level: 'silver' },
    { name: 'Speed Watcher', description: 'Completed 10 anime in one month', icon: '⚡', earned: false, level: 'copper' },
    { name: 'Community Leader', description: 'Top contributor in clan', icon: '👑', earned: false, level: 'gold' }
  ];

  const achievementColors = {
    gold: {
      border: 'border-yellow-500/70',
      bg: isNightMode ? 'bg-yellow-500/20' : 'bg-yellow-50',
      text: isNightMode ? 'text-yellow-400' : 'text-yellow-600',
      glow: 'shadow-yellow-500/20'
    },
    silver: {
      border: 'border-gray-400/70',
      bg: isNightMode ? 'bg-gray-400/20' : 'bg-gray-50',
      text: isNightMode ? 'text-gray-300' : 'text-gray-600',
      glow: 'shadow-gray-400/20'
    },
    copper: {
      border: 'border-orange-600/70',
      bg: isNightMode ? 'bg-orange-600/20' : 'bg-orange-50',
      text: isNightMode ? 'text-orange-400' : 'text-orange-600',
      glow: 'shadow-orange-600/20'
    }
  };

  return (
    <div className={`min-h-screen overflow-hidden transition-all duration-300 ${
      isNightMode
        ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900'
        : 'bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50'
    }`}>
      <div className="h-screen overflow-y-auto hide-scrollbar">
      
      {/* Floating Sakura Petals */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
        {[...Array(5)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-pink-300 rounded-full opacity-20"
            style={{
              left: `${Math.random() * 100}%`,
              top: `-10px`
            }}
            animate={{
              y: window.innerHeight + 50,
              x: [0, 30, -30, 0],
              rotate: 360,
              opacity: [0.2, 0.05, 0.2]
            }}
            transition={{
              duration: Math.random() * 3 + 10,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "linear"
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-4xl mx-auto p-4">
        
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={() => navigate('/home')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              isNightMode ? 'text-gray-300 hover:text-white hover:bg-gray-700' : 'text-gray-600 hover:text-gray-800 hover:bg-white'
            }`}
          >
            <FaArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </button>
          
          <h1 className={`text-2xl font-bold flex items-center space-x-2 ${
            isNightMode ? 'text-white' : 'text-gray-800'
          }`}>
            <span>{currentClan.icon}</span>
            <span>{currentClan.title}</span>
          </h1>
        </div>

        {/* Profile Header Card */}
        <motion.div
          className={`rounded-2xl p-6 mb-6 backdrop-blur-sm ${
            isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
          }`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col md:flex-row items-center md:items-start space-y-4 md:space-y-0 md:space-x-6">
            
            {/* Avatar Section */}
            <div className="relative">
              <div className={`w-24 h-24 rounded-full flex items-center justify-center text-4xl bg-gradient-to-r from-${currentClan.colors.primary} to-${currentClan.colors.secondary} shadow-lg`}>
                {user.avatar}
              </div>
              <button
                onClick={() => setIsEditingAvatar(true)}
                className={`absolute -bottom-2 -right-2 w-8 h-8 rounded-full flex items-center justify-center transition-colors ${
                  isNightMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                }`}
              >
                <FaCamera className="w-3 h-3" />
              </button>
            </div>

            {/* User Info */}
            <div className="flex-1 text-center md:text-left">
              <h2 className={`text-2xl font-bold mb-1 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                {user.username}
              </h2>
              <div className="flex items-center justify-center md:justify-start space-x-2 mb-2">
                <span className={`px-3 py-1 rounded-full text-sm font-semibold bg-gradient-to-r from-${currentClan.colors.primary} to-${currentClan.colors.secondary} text-white`}>
                  {user.clan} Clan
                </span>
                <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                  isNightMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'
                }`}>
                  {user.rank} • Level {user.level}
                </span>
              </div>
              
              {/* Bio Section */}
              <div className="mt-4">
                {isEditingBio ? (
                  <div className="space-y-2">
                    <textarea
                      value={tempBio}
                      onChange={(e) => setTempBio(e.target.value)}
                      className={`w-full p-3 rounded-lg border resize-none ${
                        isNightMode 
                          ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                          : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'
                      }`}
                      rows={3}
                      maxLength={200}
                      placeholder="Tell us about yourself..."
                    />
                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {tempBio.length}/200
                      </span>
                      <div className="flex space-x-2">
                        <button
                          onClick={handleSaveBio}
                          className="px-3 py-1 bg-green-500 text-white rounded-lg text-sm hover:bg-green-600 transition-colors"
                        >
                          <FaSave className="w-3 h-3" />
                        </button>
                        <button
                          onClick={handleCancelBio}
                          className="px-3 py-1 bg-gray-500 text-white rounded-lg text-sm hover:bg-gray-600 transition-colors"
                        >
                          <FaTimes className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-start space-x-2">
                    <p className={`text-sm leading-relaxed ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      {user.bio}
                    </p>
                    <button
                      onClick={() => setIsEditingBio(true)}
                      className={`p-1 rounded transition-colors ${
                        isNightMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
                      }`}
                    >
                      <FaEdit className="w-3 h-3" />
                    </button>
                  </div>
                )}
              </div>

              {/* Stats */}
              <div className="flex items-center justify-center md:justify-start space-x-6 mt-4 text-sm">
                <div className="text-center">
                  <div className={`font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>{user.posts}</div>
                  <div className={`${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>Posts</div>
                </div>
                <div className="text-center">
                  <div className={`font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>{user.followers}</div>
                  <div className={`${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>Followers</div>
                </div>
                <div className="text-center">
                  <div className={`font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>{user.following}</div>
                  <div className={`${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>Following</div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          {/* Anime Watched */}
          <motion.div
            className={`p-4 rounded-xl backdrop-blur-sm cursor-pointer transition-all hover:scale-105 ${
              isNightMode ? 'bg-gray-800/90 hover:bg-gray-700/90' : 'bg-white/90 hover:bg-gray-50/90'
            }`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0 }}
            onClick={() => setActiveListModal('watched')}
          >
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${isNightMode ? 'bg-blue-500/20' : 'bg-blue-100'}`}>
                <FaEye className={`w-4 h-4 ${isNightMode ? 'text-blue-400' : 'text-blue-600'}`} />
              </div>
              <div>
                <div className={`text-lg font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                  {user.animeWatched}
                </div>
                <div className={`text-xs ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  Anime Watched
                </div>
              </div>
            </div>
          </motion.div>

          {/* Completed */}
          <motion.div
            className={`p-4 rounded-xl backdrop-blur-sm cursor-pointer transition-all hover:scale-105 ${
              isNightMode ? 'bg-gray-800/90 hover:bg-gray-700/90' : 'bg-white/90 hover:bg-gray-50/90'
            }`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            onClick={() => setActiveListModal('completed')}
          >
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${isNightMode ? 'bg-green-500/20' : 'bg-green-100'}`}>
                <FaTrophy className={`w-4 h-4 ${isNightMode ? 'text-green-400' : 'text-green-600'}`} />
              </div>
              <div>
                <div className={`text-lg font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                  {user.animeCompleted}
                </div>
                <div className={`text-xs ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  Completed
                </div>
              </div>
            </div>
          </motion.div>

          {/* Currently Watching */}
          <motion.div
            className={`p-4 rounded-xl backdrop-blur-sm cursor-pointer transition-all hover:scale-105 ${
              isNightMode ? 'bg-gray-800/90 hover:bg-gray-700/90' : 'bg-white/90 hover:bg-gray-50/90'
            }`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            onClick={() => setActiveListModal('watching')}
          >
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${isNightMode ? 'bg-red-500/20' : 'bg-red-100'}`}>
                <FaFire className={`w-4 h-4 ${isNightMode ? 'text-red-400' : 'text-red-600'}`} />
              </div>
              <div className="flex-1 min-w-0">
                <div className={`text-lg font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                  {user.currentlyWatching.length}
                </div>
                <div className={`text-xs truncate ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  {user.currentlyWatching.length > 0 ? user.currentlyWatching[0] : 'Currently Watching'}
                </div>
              </div>
            </div>
          </motion.div>

          {/* Plan to Watch */}
          <motion.div
            className={`p-4 rounded-xl backdrop-blur-sm cursor-pointer transition-all hover:scale-105 ${
              isNightMode ? 'bg-gray-800/90 hover:bg-gray-700/90' : 'bg-white/90 hover:bg-gray-50/90'
            }`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            onClick={() => setActiveListModal('planToWatch')}
          >
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${isNightMode ? 'bg-purple-500/20' : 'bg-purple-100'}`}>
                <FaCalendar className={`w-4 h-4 ${isNightMode ? 'text-purple-400' : 'text-purple-600'}`} />
              </div>
              <div>
                <div className={`text-lg font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                  {user.planToWatch}
                </div>
                <div className={`text-xs ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  Plan to Watch
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Achievements */}
        <motion.div
          className={`rounded-2xl p-6 backdrop-blur-sm ${
            isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
          }`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <h3 className={`text-xl font-bold mb-4 flex items-center space-x-2 ${
            isNightMode ? 'text-white' : 'text-gray-800'
          }`}>
            <FaTrophy className="text-yellow-500" />
            <span>Achievements</span>
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {achievements.map((achievement, index) => {
              const colors = achievementColors[achievement.level];
              return (
                <motion.div
                  key={achievement.name}
                  className={`p-4 rounded-lg border-2 transition-all hover:scale-105 ${
                    achievement.earned
                      ? `${colors.border} ${colors.bg} shadow-lg ${colors.glow}`
                      : isNightMode
                        ? 'border-gray-600 bg-gray-700/50'
                        : 'border-gray-200 bg-gray-50'
                  }`}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`text-2xl ${achievement.earned ? '' : 'grayscale opacity-50'}`}>
                      {achievement.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <div className={`font-semibold truncate ${
                          achievement.earned
                            ? colors.text
                            : isNightMode ? 'text-gray-400' : 'text-gray-500'
                        }`}>
                          {achievement.name}
                        </div>
                        {achievement.earned && (
                          <div className={`px-2 py-0.5 rounded-full text-xs font-bold ${
                            achievement.level === 'gold' ? 'bg-yellow-500 text-white' :
                            achievement.level === 'silver' ? 'bg-gray-400 text-white' :
                            'bg-orange-600 text-white'
                          }`}>
                            {achievement.level.toUpperCase()}
                          </div>
                        )}
                      </div>
                      <div className={`text-xs mt-1 ${
                        isNightMode ? 'text-gray-400' : 'text-gray-500'
                      }`}>
                        {achievement.description}
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </motion.div>
      </div>

      {/* Avatar Selection Modal */}
      <AnimatePresence>
        {isEditingAvatar && (
          <motion.div
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className={`rounded-2xl p-6 max-w-md w-full ${
                isNightMode ? 'bg-gray-800' : 'bg-white'
              }`}
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <h3 className={`text-lg font-bold mb-4 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                Choose Avatar
              </h3>
              
              <div className="grid grid-cols-5 gap-3 mb-4">
                {avatarOptions.map((avatar) => (
                  <button
                    key={avatar}
                    onClick={() => {
                      setUser({ ...user, avatar });
                      setIsEditingAvatar(false);
                    }}
                    className={`w-12 h-12 rounded-full text-2xl flex items-center justify-center transition-all ${
                      user.avatar === avatar
                        ? `bg-gradient-to-r from-${currentClan.colors.primary} to-${currentClan.colors.secondary} shadow-lg`
                        : isNightMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-100 hover:bg-gray-200'
                    }`}
                  >
                    {avatar}
                  </button>
                ))}
              </div>
              
              <button
                onClick={() => setIsEditingAvatar(false)}
                className={`w-full py-2 rounded-lg transition-colors ${
                  isNightMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Cancel
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Anime List Modals */}
      <AnimatePresence>
        {activeListModal && (
          <motion.div
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className={`rounded-2xl p-6 max-w-2xl w-full max-h-[80vh] overflow-hidden ${
                isNightMode ? 'bg-gray-800' : 'bg-white'
              }`}
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className={`text-xl font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                  {activeListModal === 'watched' && `Anime Watched (${user.animeWatched})`}
                  {activeListModal === 'completed' && `Completed Anime (${user.animeCompleted})`}
                  {activeListModal === 'watching' && `Currently Watching (${user.currentlyWatching.length})`}
                  {activeListModal === 'planToWatch' && `Plan to Watch (${user.planToWatch})`}
                </h3>
                <button
                  onClick={() => setActiveListModal(null)}
                  className={`p-2 rounded-lg transition-colors ${
                    isNightMode ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <FaTimes className="w-4 h-4" />
                </button>
              </div>

              <div className={`max-h-96 overflow-y-auto hide-scrollbar ${
                isNightMode ? 'bg-gray-700/30' : 'bg-gray-50'
              } rounded-lg p-4`}>
                {activeListModal === 'watched' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {animeWatchedList.map((anime, index) => (
                      <div
                        key={index}
                        className={`p-3 rounded-lg transition-colors ${
                          isNightMode ? 'bg-gray-600/50 hover:bg-gray-600' : 'bg-white hover:bg-gray-50'
                        }`}
                      >
                        <div className={`font-medium ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                          {anime}
                        </div>
                        <div className={`text-xs mt-1 flex items-center space-x-1 ${isNightMode ? 'text-blue-400' : 'text-blue-600'}`}>
                          <FaEye className="w-3 h-3" />
                          <span>Completed</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {activeListModal === 'completed' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {completedList.map((anime, index) => (
                      <div
                        key={index}
                        className={`p-3 rounded-lg transition-colors ${
                          isNightMode ? 'bg-gray-600/50 hover:bg-gray-600' : 'bg-white hover:bg-gray-50'
                        }`}
                      >
                        <div className={`font-medium ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                          {anime}
                        </div>
                        <div className={`text-xs mt-1 flex items-center space-x-1 ${isNightMode ? 'text-green-400' : 'text-green-600'}`}>
                          <FaTrophy className="w-3 h-3" />
                          <span>Recently Completed</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {activeListModal === 'watching' && (
                  <div className="space-y-3">
                    {user.currentlyWatching.map((anime, index) => (
                      <div
                        key={index}
                        className={`p-4 rounded-lg transition-colors ${
                          isNightMode ? 'bg-gray-600/50 hover:bg-gray-600' : 'bg-white hover:bg-gray-50'
                        }`}
                      >
                        <div className={`font-medium ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                          {anime}
                        </div>
                        <div className={`text-xs mt-1 flex items-center space-x-1 ${isNightMode ? 'text-red-400' : 'text-red-600'}`}>
                          <FaFire className="w-3 h-3" />
                          <span>Currently Watching</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {activeListModal === 'planToWatch' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {planToWatchList.map((anime, index) => (
                      <div
                        key={index}
                        className={`p-3 rounded-lg transition-colors ${
                          isNightMode ? 'bg-gray-600/50 hover:bg-gray-600' : 'bg-white hover:bg-gray-50'
                        }`}
                      >
                        <div className={`font-medium ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                          {anime}
                        </div>
                        <div className={`text-xs mt-1 flex items-center space-x-1 ${isNightMode ? 'text-purple-400' : 'text-purple-600'}`}>
                          <FaCalendar className="w-3 h-3" />
                          <span>Plan to Watch</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      </div>
    </div>
  );
};

export default Profile;
