{"name": "user-service", "version": "1.0.0", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "cross-env NODE_ENV=test jest --runInBand --detectOpenHandles", "test:watch": "npm test -- --watch", "test:coverage": "npm test -- --coverage"}, "jest": {"setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "testEnvironment": "node"}, "author": "", "license": "ISC", "description": "", "dependencies": {"aws-sdk": "^2.1692.0", "axios": "^1.10.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-validator": "^7.2.1", "http-errors": "^2.0.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^8.16.3", "morgan": "^1.10.0", "multer": "^2.0.1", "nodemon": "^3.1.10", "pg": "^8.11.0", "rate-limiter-flexible": "^7.1.1", "redis": "^5.6.0", "winston": "^3.17.0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@types/jest": "^30.0.0", "babel-jest": "^30.0.4", "cross-env": "^7.0.3", "jest": "^30.0.4", "mongodb-memory-server": "^10.1.4", "supertest": "^7.1.3"}}