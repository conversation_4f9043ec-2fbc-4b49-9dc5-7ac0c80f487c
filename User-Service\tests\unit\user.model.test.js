import User from '../../models/user.model.js';

describe('User Model', () => {
  it('should create a new user', async () => {
    const userData = {
      username: `testuser_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      password: 'password123'
    };

    const user = await User.create(userData);
    expect(user).toHaveProperty('_id');
    expect(user.username).toBe(userData.username);
    expect(user.email).toBe(userData.email);
    expect(user.password).not.toBe(userData.password);
  });

  it('should not create user with duplicate email', async () => {
    const email = `test_${Date.now()}@example.com`;
    
    await User.create({
      username: 'user1',
      email: email,
      password: 'password123'
    });

    await expect(
      User.create({
        username: 'user2',
        email: email,
        password: 'password456'
      })
    ).rejects.toThrow();
  });

  it('should compare passwords correctly', async () => {
    const userData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123'
    };

    const user = new User(userData);
    await user.save();

    const isMatch = await user.comparePassword('password123');
    expect(isMatch).toBe(true);

    const isNotMatch = await user.comparePassword('wrongpassword');
    expect(isNotMatch).toBe(false);
  });

  it('should add XP and level up correctly', async () => {
    const user = new User({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
      xp: 90,
      level: 1
    });
    await user.save();

    await user.addXP(20);
    expect(user.xp).toBe(10);
    expect(user.level).toBe(2);
  });
});