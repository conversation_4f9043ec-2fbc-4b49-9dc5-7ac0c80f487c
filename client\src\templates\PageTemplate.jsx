// 📜 AnimeVerse Page Template - Consistent Scrollbar Pattern
// Use this template for all new pages to ensure invisible scrollbars

import React from 'react';
import { motion } from 'framer-motion';
import { FaArrowLeft } from 'react-icons/fa';
import { useTheme } from '../context/ThemeContext';
import { useNavigate } from 'react-router-dom';

const PageTemplate = () => {
  const { isNightMode } = useTheme();
  const navigate = useNavigate();

  return (
    // 🔧 SCROLLBAR PATTERN - ALWAYS USE THIS STRUCTURE:
    <div className={`min-h-screen overflow-hidden transition-all duration-300 ${
      isNightMode 
        ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900' 
        : 'bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50'
    }`}>
      {/* ✅ Inner scrollable container with invisible scrollbar */}
      <div className="h-screen overflow-y-auto hide-scrollbar">
        
        {/* 🌸 Floating Sakura Petals - Optional but recommended */}
        <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-pink-300 rounded-full opacity-20"
              style={{
                left: `${Math.random() * 100}%`,
                top: `-10px`
              }}
              animate={{
                y: window.innerHeight + 50,
                x: [0, 30, -30, 0],
                rotate: 360,
                opacity: [0.2, 0.05, 0.2]
              }}
              transition={{
                duration: Math.random() * 3 + 12,
                repeat: Infinity,
                delay: Math.random() * 5,
                ease: "linear"
              }}
            />
          ))}
        </div>

        {/* 📱 Main Content Container */}
        <div className="relative z-10 max-w-6xl mx-auto p-4">
          
          {/* 🔙 Header with Back Button */}
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={() => navigate('/home')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                isNightMode ? 'text-gray-300 hover:text-white hover:bg-gray-700' : 'text-gray-600 hover:text-gray-800 hover:bg-white'
              }`}
            >
              <FaArrowLeft className="w-4 h-4" />
              <span>Back to Home</span>
            </button>
            
            <h1 className={`text-2xl font-bold flex items-center space-x-2 ${
              isNightMode ? 'text-white' : 'text-gray-800'
            }`}>
              <span>🎌</span>
              <span>Page Title</span>
            </h1>
          </div>

          {/* 📋 Page Content */}
          <div className={`rounded-2xl p-6 mb-6 backdrop-blur-sm ${
            isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
          }`}>
            <h2 className={`text-xl font-bold mb-4 ${
              isNightMode ? 'text-white' : 'text-gray-800'
            }`}>
              Your Content Here
            </h2>
            
            {/* Add your page-specific content here */}
            <p className={`${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
              This is where your page content goes...
            </p>
          </div>

          {/* 📜 Scrollable Content Areas - Use hide-scrollbar class */}
          <div className={`rounded-2xl p-6 backdrop-blur-sm ${
            isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
          }`}>
            <h3 className={`text-lg font-bold mb-4 ${
              isNightMode ? 'text-white' : 'text-gray-800'
            }`}>
              Scrollable Section
            </h3>
            
            {/* ✅ Any scrollable area within the page should use hide-scrollbar */}
            <div className="max-h-64 overflow-y-auto hide-scrollbar">
              {/* Scrollable content */}
              {[...Array(20)].map((_, i) => (
                <div key={i} className={`p-3 mb-2 rounded-lg ${
                  isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                }`}>
                  Scrollable item {i + 1}
                </div>
              ))}
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default PageTemplate;

/*
🔧 SCROLLBAR PATTERN CHECKLIST:

✅ Outer container: 
   - min-h-screen overflow-hidden
   - Background gradient
   - transition-all duration-300

✅ Inner container:
   - h-screen overflow-y-auto hide-scrollbar
   - This creates the main page scroll

✅ Any sub-scrollable areas:
   - max-h-[size] overflow-y-auto hide-scrollbar
   - Use for modals, lists, etc.

✅ CSS Requirements:
   - hide-scrollbar class must be in style.css
   - Applied to all scrollable containers

🌸 Optional Enhancements:
   - Floating sakura petals
   - Backdrop blur effects
   - Smooth animations
   - Responsive design

📱 Pages Using This Pattern:
   ✅ Home.jsx
   ✅ Profile.jsx  
   ✅ Clan.jsx
   ✅ AnimeRealm.jsx
   
🚀 Future Pages Should Follow:
   - Eden Market
   - Festivals
   - Sensei Chat
   - Spirit Bell
   - Settings
   - Any new pages

💡 Remember:
   - Always test scrolling on different screen sizes
   - Ensure content is accessible via keyboard navigation
   - Maintain consistent spacing and typography
   - Use backdrop-blur-sm for glass morphism effect
*/
