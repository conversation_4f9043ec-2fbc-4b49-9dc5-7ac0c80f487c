const User = require('../models/user.model');

class UserService {
  static async getUserById(userId) {
    try {
      const user = await User.findById(userId)
        .select('-password')
        .populate('mainCommunity', 'name image')
        .populate('joinedCommunities', 'name image');
      
      if (!user) {
        throw new Error('User not found');
      }
      
      return user;
    } catch (error) {
      throw error;
    }
  }
  
  static async updateUserProfile(userId, updateData) {
    try {
      const allowedUpdates = ['username', 'avatar', 'bio', 'favoriteAnime', 'settings'];
      const updates = Object.keys(updateData);
      
      const isValidOperation = updates.every(update => allowedUpdates.includes(update));
      
      if (!isValidOperation) {
        throw new Error('Invalid updates!');
      }
      
      const user = await User.findByIdAndUpdate(
        userId,
        updateData,
        { new: true, runValidators: true }
      ).select('-password');
      
      if (!user) {
        throw new Error('User not found');
      }
      
      return user;
    } catch (error) {
      throw error;
    }
  }
  
  static async followUser(userId, targetUserId) {
    try {
      if (userId.toString() === targetUserId.toString()) {
        throw new Error('You cannot follow yourself');
      }
      
      const [user, targetUser] = await Promise.all([
        User.findById(userId),
        User.findById(targetUserId)
      ]);
      
      if (!user || !targetUser) {
        throw new Error('User not found');
      }
      
      // Check if already following
      if (user.following.includes(targetUserId)) {
        throw new Error('Already following this user');
      }
      
      // Add to following list
      user.following.push(targetUserId);
      targetUser.followers.push(userId);
      
      await Promise.all([user.save(), targetUser.save()]);
      
      return { success: true };
    } catch (error) {
      throw error;
    }
  }
  
  static async unfollowUser(userId, targetUserId) {
    try {
      const [user, targetUser] = await Promise.all([
        User.findById(userId),
        User.findById(targetUserId)
      ]);
      
      if (!user || !targetUser) {
        throw new Error('User not found');
      }
      
      // Check if not following
      if (!user.following.includes(targetUserId)) {
        throw new Error('Not following this user');
      }
      
      // Remove from following list
      user.following = user.following.filter(id => id.toString() !== targetUserId.toString());
      targetUser.followers = targetUser.followers.filter(id => id.toString() !== userId.toString());
      
      await Promise.all([user.save(), targetUser.save()]);
      
      return { success: true };
    } catch (error) {
      throw error;
    }
  }
  
  static async searchUsers(query) {
    try {
      const users = await User.find({
        $or: [
          { username: { $regex: query, $options: 'i' } },
          { email: { $regex: query, $options: 'i' } }
        ]
      }).select('username avatar bio');
      
      return users;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = UserService;