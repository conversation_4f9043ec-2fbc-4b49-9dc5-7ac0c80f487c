const axios = require('axios');
const createError = require('http-errors');

const JIKAN_API = 'https://api.jikan.moe/v4';

const searchAnime = async (req, res, next) => {
  try {
    const { q, page = 1, limit = 10 } = req.query;
    
    if (!q || q.trim().length < 2) {
      throw createError(400, 'Search query must be at least 2 characters');
    }
    
    const response = await axios.get(`${JIKAN_API}/anime`, {
      params: {
        q,
        page,
        limit,
        sfw: true
      },
      timeout: 5000
    });
    
    // Transform response
    const results = response.data.data.map(anime => ({
      mal_id: anime.mal_id,
      title: anime.title,
      title_japanese: anime.title_japanese,
      images: anime.images,
      episodes: anime.episodes,
      status: anime.status,
      score: anime.score,
      synopsis: anime.synopsis
    }));
    
    res.json({
      pagination: response.data.pagination,
      results
    });
  } catch (error) {
    if (error.response) {
      next(createError(error.response.status, 'Jikan API error'));
    } else if (error.request) {
      next(createError(504, 'Jikan API timeout'));
    } else {
      next(createError(500, 'Anime search failed'));
    }
  }
};

// Additional anime endpoints can be added here
const getAnimeById = async (req, res, next) => {
  // Implementation for getting anime by ID
};

module.exports = {
  searchAnime,
  getAnimeById
};