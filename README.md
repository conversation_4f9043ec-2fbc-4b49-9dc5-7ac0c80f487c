# 🌸 AnimeVerse - Social Platform for Anime Fans

AnimeVerse is a bilingual social web app where anime fans share thoughts, create posts, videos, and stories. Each fan belongs to one main anime community with themed ranks, and can also join diverse groups to discuss other anime.

## 🎯 Features

- **User Profiles**: Bio, avatar, favorite anime, personal anime lists with ratings & reviews
- **Content Creation**: Posts (text/images/polls), short videos, stories (24h ephemeral content)
- **Social Graph**: Follow users, like, comment, share content
- **Communities**: Choose 1 main anime community with themed ranks and exclusive content
- **Groups**: Join or create discussion groups about any anime topic
- **Events**: Location-based event discovery, RSVP and share
- **Ranking & Gamification**: XP, levels, themed ranks, badges, seasonal titles
- **Tournaments & Contests**: Quizzes, blind tests, fan art contests, community challenges
- **Marketplace**: Buy & sell merchandise and digital goods

## 🏗️ Project Structure

```
animeverse/
├── client/                 # React frontend
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── context/
│   │   ├── utils/
│   │   └── styles/
│   ├── package.json
│   └── tailwind.config.js
├── server/                 # Express backend
│   ├── src/
│   │   ├── controllers/
│   │   ├── middleware/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── config/
│   │   └── utils/
│   ├── package.json
│   └── .env
├── shared/                 # Shared types/utilities
└── README.md
```

## 🚀 Tech Stack

**Frontend:**
- React.js + Tailwind CSS (responsive, dark/light mode)

**Backend:**
- Node.js with Express.js
- PostgreSQL for user data, posts, reviews
- Redis for caching & session management

**Media & Storage:**
- AWS S3 or Cloudflare R2 for images/videos
- FFmpeg for video processing

**Real-time Features:**
- WebSockets (notifications, live event updates)

**Search:**
- Elasticsearch for fast search

**Analytics:**
- Google Analytics + Amplitude

## 🛠️ Development Setup

### Prerequisites
- Node.js (v16 or higher)
- PostgreSQL
- Redis (optional for development)

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd animeverse
```

2. Install dependencies
```bash
# Install frontend dependencies
cd client
npm install

# Install backend dependencies
cd ../server
npm install
```

3. Set up environment variables
```bash
cp server/.env.example server/.env
# Edit .env with your configuration
```

4. Set up database
```bash
# Create PostgreSQL database
createdb animeverse
```

5. Run the application
```bash
# Run both frontend and backend
cd server
npm run dev:full
```

## 📝 Development Status

- [x] Project structure setup
- [ ] Authentication system
- [ ] User profiles
- [ ] Content creation
- [ ] Social features
- [ ] Communities & groups
- [ ] Events system
- [ ] Gamification
- [ ] Marketplace

## 🌍 Languages

- English
- French (Français)

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please read our contributing guidelines before submitting PRs.

## 📞 Contact

For questions or support, please contact the development team.
