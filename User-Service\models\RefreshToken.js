const db = require('../config/db');

const RefreshToken = {
  create: async (userId, token, expiresAt) => {
    const { rows } = await db.query(
      `INSERT INTO refresh_tokens (user_id, token, expires_at) 
       VALUES ($1, $2, $3) RETURNING *`,
      [userId, token, expiresAt]
    );
    return rows[0];
  },

  findByToken: async (token) => {
    const { rows } = await db.query(
      `SELECT * FROM refresh_tokens 
       WHERE token = $1 AND expires_at > NOW()`,
      [token]
    );
    return rows[0];
  },

  revoke: async (token) => {
    await db.query(
      `DELETE FROM refresh_tokens WHERE token = $1`,
      [token]
    );
  },

  revokeAllForUser: async (userId) => {
    await db.query(
      `DELETE FROM refresh_tokens WHERE user_id = $1`,
      [userId]
    );
  }
};

module.exports = RefreshToken;