# 🌸 AnimeVerse Signup Page

A beautiful, anime-themed signup page with modern animations and responsive design.

## ✨ Features

### 🎨 **Anime-Themed Design**
- **Gradient backgrounds** with anime-inspired colors (purple, pink, blue)
- **Floating sakura petals** animation
- **Anime character display** (Luffy placeholder)
- **Custom anime color palette** throughout the interface

### 🔐 **Advanced Authentication**
- **Username validation** (3-20 characters, alphanumeric + underscores)
- **Email validation** with proper regex
- **Strong password requirements** (8+ chars, mixed case, numbers)
- **Password confirmation** matching
- **Show/hide password** toggle buttons
- **Admin mode** (Ctrl+Shift+A) for admin registration

### 🎌 **Anime Community Features**
- **Favorite anime selection** from popular titles
- **Custom anime input** option
- **Community-themed styling** based on anime selection

### 🚀 **Social Login Options**
- Google, Facebook, Twitter, Discord integration ready
- **Animated hover effects** on social buttons
- **Modern icon design** with react-icons

### 📱 **Responsive & Interactive**
- **Fully responsive** design (mobile, tablet, desktop)
- **Framer Motion animations** throughout
- **Dark/Light mode** support via ThemeContext
- **Loading states** with animated spinners
- **Toast notifications** for user feedback

### 🎭 **Advanced Animations**
- **Page entrance** animations with scale and rotation
- **Floating elements** with random movement patterns
- **Form field focus** animations
- **Button hover effects** with scale and rotation
- **Background particle** animations

## 🛠️ **Technical Implementation**

### **Dependencies Used:**
- `react` & `react-dom` - Core React functionality
- `react-router-dom` - Navigation and routing
- `framer-motion` - Advanced animations
- `react-icons` - Icon library (Font Awesome)
- `react-hot-toast` - Toast notifications
- `axios` - HTTP client for API calls

### **Custom Utilities:**
- `validateEmail()` - Email format validation
- `validatePassword()` - Strong password validation
- `validateUsername()` - Username format validation
- `axiosCookies` - Configured axios instance with auth

### **Context & State Management:**
- `ThemeContext` - Dark/light mode and anime themes
- Local state management for form fields
- Error handling and loading states

## 🎯 **Usage**

```jsx
import Signup from './pages/auth/Signup';

// Use in your router
<Route path="/signup" element={<Signup />} />
```

## 🔧 **Customization**

### **Anime Themes:**
You can customize the anime themes in `utils/helper.js`:

```javascript
const getCommunityTheme = (community) => {
  const themes = {
    'one-piece': { primary: 'from-red-500 to-orange-500' },
    'naruto': { primary: 'from-orange-500 to-yellow-500' },
    // Add more themes...
  };
};
```

### **Animation Settings:**
Modify animations in the component by adjusting Framer Motion props:

```jsx
<motion.div
  initial={{ opacity: 0, scale: 0.8 }}
  animate={{ opacity: 1, scale: 1 }}
  transition={{ duration: 0.8 }}
>
```

## 🎨 **Color Palette**

- **Primary Purple:** `#6366f1` (anime-purple)
- **Primary Pink:** `#ec4899` (anime-pink)  
- **Primary Blue:** `#3b82f6` (anime-blue)
- **Accent Cyan:** `#06b6d4` (anime-cyan)
- **Dark Mode:** Custom dark color scheme

## 📝 **Form Validation**

- **Username:** 3-20 characters, alphanumeric + underscores
- **Email:** Valid email format required
- **Password:** 8+ characters, uppercase, lowercase, number
- **Confirm Password:** Must match password
- **Favorite Anime:** Required selection or custom input

## 🔐 **Admin Mode**

Press `Ctrl+Shift+A` to toggle admin registration mode:
- Adds admin code input field
- Changes button styling to red theme
- Sends admin flag to backend

## 🌟 **Next Steps**

1. **Add real images** to replace placeholders
2. **Implement backend API** endpoints
3. **Add more anime themes** and customization
4. **Enhance animations** with more complex sequences
5. **Add form validation** feedback animations
