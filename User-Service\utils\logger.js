const { createLogger, format, transports } = require('winston');
const path = require('path');
const { combine, timestamp, printf, colorize, errors } = format;

// Custom format for console output
const consoleFormat = printf(({ level, message, timestamp, stack }) => {
  const log = `${timestamp} [${level}]: ${stack || message}`;
  return log;
});

// Custom format for file output (JSON)
const fileFormat = combine(
  timestamp(),
  errors({ stack: true }),
  format.json()
);

// Create logger instance
const logger = createLogger({
  level: process.env.LOG_LEVEL || 'debug',
  defaultMeta: { service: 'animeverse-api' },
  transports: [
    // Console transport (colorized)
    new transports.Console({
      format: combine(
        colorize(),
        timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        errors({ stack: true }),
        consoleFormat
      )
    }),
    // File transport (errors only)
    new transports.File({
      filename: path.join(__dirname, '../logs/error.log'),
      level: 'error',
      format: fileFormat
    }),
    // File transport (all logs)
    new transports.File({
      filename: path.join(__dirname, '../logs/combined.log'),
      format: fileFormat
    })
  ],
  exceptionHandlers: [
    new transports.File({
      filename: path.join(__dirname, '../logs/exceptions.log')
    })
  ]
});

// Morgan stream for HTTP logging
logger.morganStream = {
  write: (message) => {
    logger.info(message.trim());
  }
};

module.exports = logger;