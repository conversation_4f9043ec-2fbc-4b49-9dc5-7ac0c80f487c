// Email validation utility
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Password validation utility
export const validatePassword = (password) => {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
};

// Username validation
export const validateUsername = (username) => {
  // 3-20 characters, alphanumeric and underscores only
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  return usernameRegex.test(username);
};

// Format date utility
export const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// Capitalize first letter
export const capitalizeFirst = (str) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

// Generate random anime quote
export const getRandomAnimeQuote = () => {
  const quotes = [
    "Believe in yourself and create your own destiny! - Naruto",
    "The only way to truly escape the mundane is for you to constantly be evolving. - Senku",
    "Hard work is what makes your dreams come true! - Rock Lee",
    "If you don't take risks, you can't create a future! - Monkey D. Luffy",
    "People's dreams never end! - Marshall D. Teach",
    "I want to be the very best, like no one ever was! - Ash Ketchum"
  ];
  return quotes[Math.floor(Math.random() * quotes.length)];
};

// Anime community themes
export const getCommunityTheme = (community) => {
  const themes = {
    'one-piece': {
      primary: 'from-red-500 to-orange-500',
      secondary: 'from-orange-400 to-yellow-400',
      accent: 'text-orange-500'
    },
    'naruto': {
      primary: 'from-orange-500 to-yellow-500',
      secondary: 'from-yellow-400 to-orange-400',
      accent: 'text-orange-500'
    },
    'attack-titan': {
      primary: 'from-green-600 to-emerald-600',
      secondary: 'from-emerald-400 to-green-400',
      accent: 'text-green-500'
    },
    'demon-slayer': {
      primary: 'from-purple-600 to-pink-600',
      secondary: 'from-pink-400 to-purple-400',
      accent: 'text-purple-500'
    },
    'my-hero-academia': {
      primary: 'from-blue-600 to-cyan-600',
      secondary: 'from-cyan-400 to-blue-400',
      accent: 'text-blue-500'
    },
    'dragon-ball': {
      primary: 'from-yellow-500 to-orange-500',
      secondary: 'from-orange-400 to-red-400',
      accent: 'text-yellow-500'
    }
  };
  
  return themes[community] || {
    primary: 'from-anime-purple to-anime-pink',
    secondary: 'from-anime-pink to-anime-purple',
    accent: 'text-anime-purple'
  };
};
