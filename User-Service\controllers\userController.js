const db = require('../config/db');
const bcrypt = require('bcryptjs');
const { logger } = require('../utils/logger');

const User = {
  async create({ username, email, password }) {
    const hashedPassword = await bcrypt.hash(password, 12);
    const { rows } = await db.query(
      `INSERT INTO users (username, email, password) 
       VALUES ($1, $2, $3) 
       RETURNING id, username, email, created_at`,
      [username, email, hashedPassword]
    );
    return rows[0];
  },

  async findByEmail(email) {
    const { rows } = await db.query(
      'SELECT * FROM users WHERE email = $1',
      [email]
    );
    return rows[0];
  },

  async findById(id) {
    const { rows } = await db.query(
      `SELECT id, username, email, avatar_url, bio, 
              favorite_anime, main_community, xp
       FROM users WHERE id = $1`,
      [id]
    );
    return rows[0];
  },

  async updateProfile(id, updates) {
    const { rows } = await db.query(
      `UPDATE users 
       SET avatar_url = COALESCE($1, avatar_url),
           bio = COALESCE($2, bio),
           favorite_anime = COALESCE($3, favorite_anime),
           main_community = COALESCE($4, main_community),
           updated_at = NOW()
       WHERE id = $5
       RETURNING *`,
      [
        updates.avatar_url,
        updates.bio,
        updates.favorite_anime,
        updates.main_community,
        id
      ]
    );
    return rows[0];
  },

  async addXP(userId, xp) {
    const { rows } = await db.query(
      `UPDATE users 
       SET xp = xp + $1 
       WHERE id = $2 
       RETURNING xp`,
      [xp, userId]
    );
    return rows[0].xp;
  }
};

module.exports = User;