const testRoute = (routePath) => {
  try {
    const route = require(routePath);
    console.log(`Testing ${routePath}:`);
    console.log('Type:', typeof route);
    console.log('Is router:', typeof route === 'function' && route.name === 'router');
    console.log('Methods:', Object.keys(route));
    return true;
  } catch (err) {
    console.error(`Error in ${routePath}:`, err);
    return false;
  }
};

['./routes/authRoutes', './routes/userRoutes', './routes/animeRoutes'].forEach(testRoute);