import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Heart, MessageCircle, Share, Bookmark, Plus, Search, 
  Bell, Settings, Home as HomeIcon, Users, ShoppingBag, 
  Calendar, User, Sparkles, Star, Eye
} from 'lucide-react';
import { useTheme } from '../context/ThemeContext';
import { useNavigate } from 'react-router-dom';

const Home = () => {
  const { isNightMode } = useTheme();
  const navigate = useNavigate();
  const [likedPosts, setLikedPosts] = useState(new Set());

  // Mock data for anime content with clan system (one clan per anime universe)
  const animeStories = [
    { id: 1, user: 'NarutoFan2024', avatar: '🍥', clan: 'Naruto', hasNew: true },
    { id: 2, user: 'OnePieceLover', avatar: '🏴‍☠️', clan: 'One Piece', hasNew: true },
    { id: 3, user: 'AttackOnTitan', avatar: '⚔️', clan: 'Attack on Titan', hasNew: false },
    { id: 4, user: 'DemonSlayerFan', avatar: '🗡️', clan: 'Demon Slayer', hasNew: true },
    { id: 5, user: 'MyHeroAcademia', avatar: '💥', clan: 'My Hero Academia', hasNew: false },
    { id: 6, user: 'JujutsuKaisen', avatar: '👻', clan: 'Jujutsu Kaisen', hasNew: true },
    { id: 7, user: 'DragonBallZ', avatar: '🐉', clan: 'Dragon Ball', hasNew: true },
    { id: 8, user: 'FullmetalAlchemist', avatar: '⚗️', clan: 'Fullmetal Alchemist', hasNew: false },
    { id: 9, user: 'TokyoRevengers', avatar: '🏍️', clan: 'Tokyo Revengers', hasNew: true },
    { id: 10, user: 'BleachFan', avatar: '⚡', clan: 'Bleach', hasNew: false },
  ];

  const animePosts = [
    {
      id: 1,
      user: { name: 'AnimeReviewer_Pro', avatar: '🎭', verified: true },
      content: {
        type: 'image',
        image: '/api/placeholder/500/600',
        caption: 'Just finished watching the latest Demon Slayer episode! The animation quality is absolutely insane! 🔥✨ #DemonSlayer #AnimeReview'
      },
      likes: 1247,
      comments: 89,
      timestamp: '2 hours ago',
      anime: 'Demon Slayer'
    },
    {
      id: 2,
      user: { name: 'MangaCollector', avatar: '📚', verified: false },
      content: {
        type: 'image',
        image: '/api/placeholder/500/400',
        caption: 'My latest manga haul! Finally got the complete One Piece collection 📖🏴‍☠️ Who else is collecting? #OnePiece #MangaCollection'
      },
      likes: 892,
      comments: 156,
      timestamp: '4 hours ago',
      anime: 'One Piece'
    },
    {
      id: 3,
      user: { name: 'FanArtist_Senpai', avatar: '🎨', verified: true },
      content: {
        type: 'image',
        image: '/api/placeholder/500/500',
        caption: 'Drew my favorite character from Jujutsu Kaisen! Took me 12 hours but totally worth it! 💜✨ #JujutsuKaisen #FanArt #DigitalArt'
      },
      likes: 2156,
      comments: 234,
      timestamp: '6 hours ago',
      anime: 'Jujutsu Kaisen'
    }
  ];

  const bestPosts = [
    { user: 'ReviewerPro', content: 'DS S4 Ep8 Review', likes: '12.4K', clan: 'Demon Slayer' },
    { user: 'ArtistSenpai', content: 'Luffy Gear 5 Art', likes: '8.9K', clan: 'One Piece' },
    { user: 'Collector', content: 'Rare Collection', likes: '15.2K', clan: 'Tokyo Revengers' },
    { user: 'Analyst', content: 'AOT Ending', likes: '22.1K', clan: 'Attack on Titan' }
  ];

  const upcomingEvents = [
    { event: 'Anime Expo 2024', type: 'Convention', date: 'July 4-7, 2024', location: 'Los Angeles' },
    { event: 'One Piece Film: Red Screening', type: 'Movie Night', date: 'Tomorrow 8:00 PM', location: 'Virtual' },
    { event: 'Demon Slayer Season 5 Announcement', type: 'Live Stream', date: 'Next Week', location: 'YouTube' },
    { event: 'Manga Reading Club', type: 'Community', date: 'Every Sunday', location: 'Discord' }
  ];

  const handleLike = (postId) => {
    setLikedPosts(prev => {
      const newSet = new Set(prev);
      if (newSet.has(postId)) {
        newSet.delete(postId);
      } else {
        newSet.add(postId);
      }
      return newSet;
    });
  };

  return (
    <div className={`min-h-screen transition-all duration-300 overflow-hidden ${
      isNightMode
        ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900'
        : 'bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50'
    }`}>
      
      {/* Floating Sakura Petals */}
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          className="fixed w-2 h-2 bg-pink-300 rounded-full opacity-30 pointer-events-none z-0"
          style={{
            left: `${Math.random() * 100}%`,
            top: `-10px`
          }}
          animate={{
            y: window.innerHeight + 50,
            x: [0, 30, -30, 0],
            rotate: 360
          }}
          transition={{
            duration: Math.random() * 3 + 8,
            repeat: Infinity,
            delay: Math.random() * 5,
            ease: "linear"
          }}
        />
      ))}

      <div className="flex max-w-7xl mx-auto relative z-10">
        
        {/* Left Sidebar */}
        <div className={`fixed left-0 top-0 h-screen w-64 px-6 p-3 border-r transition-all duration-300 overflow-y-auto hide-scrollbar ${
          isNightMode
            ? 'bg-gray-800/95 border-gray-700 backdrop-blur-sm'
            : 'bg-white/95 border-gray-200 backdrop-blur-sm'
        }`}>
          
          {/* Logo */}
          {/* <div className="mb-4">
            <div className="flex items-center justify-center">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500 via-pink-500 to-blue-500 flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-2xl">A</span>
              </div>
            </div>
          </div> */}

          {/* Navigation */}
          <nav className="space-y-2">
            {[
              { icon: HomeIcon, label: 'Home', active: true, path: '/home' },
              { icon: Search, label: 'Anime Realm', path: '/anime-realm' },
              { icon: Users, label: 'Clan', path: '/clan' },
              { icon: ShoppingBag, label: 'Eden Market', path: '/eden-market' },
              { icon: Calendar, label: 'Festivals', path: '/festivals' },
              { icon: MessageCircle, label: 'Sensei Chat', path: '/sensei-chat' },
              { icon: Bell, label: 'Spirit Bell', path: '/spirit-bell' },
              { icon: Settings, label: 'Settings', path: '/settings' },
              // { icon: User, label: 'Ninja Profile' }
            ].map((item) => (
              <motion.div
                key={item.label}
                className={`flex items-center space-x-3 p-3 rounded-xl cursor-pointer transition-all duration-200 ${
                  item.active
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                    : isNightMode
                      ? 'hover:bg-gray-700 text-gray-300'
                      : 'hover:bg-gray-100 text-gray-700'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => item.path && navigate(item.path)}
              >
                <item.icon className="w-6 h-6" />
                <span className="font-medium">{item.label}</span>
              </motion.div>
            ))}
          </nav>

          {/* Create Post Button */}
          <motion.button
            className="w-full mt-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white p-3 rounded-xl font-semibold flex items-center justify-center space-x-2 shadow-lg"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Plus className="w-5 h-5" />
            <span>Create Post</span>
          </motion.button>
        </div>

        {/* Main Content */}
        <div className="flex-1 ml-64 mr-80 h-screen overflow-y-auto hide-scrollbar">
          <div className="max-w-2xl mx-auto py-8 px-4">
            
            {/* Stories Section */}
            <div className={`mb-8 p-6 rounded-2xl border transition-all duration-300 ${
              isNightMode 
                ? 'bg-gray-800/50 border-gray-700 backdrop-blur-sm' 
                : 'bg-white/70 border-gray-200 backdrop-blur-sm'
            }`}>
              <h3 className={`text-lg font-semibold mb-4 flex items-center ${
                isNightMode ? 'text-white' : 'text-gray-800'
              }`}>
                <Sparkles className="w-5 h-5 mr-2 text-purple-500" />
                Clan Chronicles
              </h3>
              
              <div className="flex space-x-4 overflow-x-auto pb-2 hide-scrollbar">
                {animeStories.map((story) => (
                  <motion.div
                    key={story.id}
                    className="flex-shrink-0 text-center cursor-pointer"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <div className={`relative w-16 h-16 rounded-full p-1 ${
                      story.hasNew 
                        ? 'bg-gradient-to-tr from-purple-500 via-pink-500 to-yellow-500' 
                        : 'bg-gray-300'
                    }`}>
                      <div className={`w-full h-full rounded-full flex items-center justify-center text-2xl ${
                        isNightMode ? 'bg-gray-800' : 'bg-white'
                      }`}>
                        {story.avatar}
                      </div>
                    </div>
                    <div className="text-center mt-2">
                      <p className={`text-xs max-w-[64px] truncate ${
                        isNightMode ? 'text-gray-300' : 'text-gray-600'
                      }`}>
                        {story.user}
                      </p>
                      <p className={`text-xs max-w-[64px] truncate font-semibold ${
                        isNightMode ? 'text-purple-400' : 'text-purple-600'
                      }`}>
                        {story.clan}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Posts Feed */}
            <div className="space-y-8">
              {animePosts.map((post) => (
                <motion.div
                  key={post.id}
                  className={`rounded-2xl border overflow-hidden transition-all duration-300 ${
                    isNightMode 
                      ? 'bg-gray-800/50 border-gray-700 backdrop-blur-sm' 
                      : 'bg-white/70 border-gray-200 backdrop-blur-sm'
                  }`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  {/* Post Header */}
                  <div className="p-4 flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold">
                        {post.user.avatar}
                      </div>
                      <div>
                        <div className="flex items-center space-x-1">
                          <span className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            {post.user.name}
                          </span>
                          {post.user.verified && (
                            <Star className="w-4 h-4 text-yellow-500 fill-current" />
                          )}
                        </div>
                        <span className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          {post.timestamp} • {post.anime}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Post Image */}
                  <div className="relative">
                    <img 
                      src={post.content.image} 
                      alt="Post content"
                      className="w-full h-96 object-cover"
                    />
                    <div className="absolute top-4 right-4">
                      <span className="bg-black/50 text-white px-2 py-1 rounded-full text-xs backdrop-blur-sm">
                        #{post.anime}
                      </span>
                    </div>
                  </div>

                  {/* Post Actions */}
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-4">
                        <motion.button
                          onClick={() => handleLike(post.id)}
                          className={`flex items-center space-x-1 ${
                            likedPosts.has(post.id) ? 'text-red-500' : isNightMode ? 'text-gray-300' : 'text-gray-600'
                          }`}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <Heart className={`w-6 h-6 ${likedPosts.has(post.id) ? 'fill-current' : ''}`} />
                        </motion.button>
                        
                        <motion.button
                          className={isNightMode ? 'text-gray-300' : 'text-gray-600'}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <MessageCircle className="w-6 h-6" />
                        </motion.button>
                        
                        <motion.button
                          className={isNightMode ? 'text-gray-300' : 'text-gray-600'}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <Share className="w-6 h-6" />
                        </motion.button>
                      </div>
                      
                      <motion.button
                        className={isNightMode ? 'text-gray-300' : 'text-gray-600'}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Bookmark className="w-6 h-6" />
                      </motion.button>
                    </div>

                    {/* Likes Count */}
                    <p className={`font-semibold mb-2 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                      {post.likes.toLocaleString()} likes
                    </p>

                    {/* Caption */}
                    <p className={isNightMode ? 'text-gray-300' : 'text-gray-700'}>
                      <span className="font-semibold">{post.user.name}</span> {post.content.caption}
                    </p>

                    {/* Comments */}
                    <button className={`text-sm mt-2 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      View all {post.comments} comments
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Sidebar */}
        <div className={`fixed right-0 top-0 h-screen w-80 p-6 border-l overflow-y-auto hide-scrollbar transition-all duration-300 ${
          isNightMode
            ? 'bg-gray-800/95 border-gray-700 backdrop-blur-sm'
            : 'bg-white/95 border-gray-200 backdrop-blur-sm'
        }`}>

          {/* Ninja Profile Access - Click to view full profile */}
          <motion.div
            className={`p-4 rounded-xl mb-6 cursor-pointer transition-all duration-200 ${
              isNightMode ? 'bg-gray-700/50 hover:bg-gray-600/50' : 'bg-gray-50/50 hover:bg-gray-100/50'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => navigate('/profile')}
          >
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-lg">
                🎭
              </div>
              <div className="flex-1">
                <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                  AnimeOtaku_2024
                </h3>
                <p className={`text-sm ${isNightMode ? 'text-purple-400' : 'text-purple-600'}`}>
                  Naruto Clan
                </p>
                <p className={`text-xs ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  Level 42 • 1,247 episodes watched
                </p>
              </div>
              <div className="flex flex-col items-center">
                <div className={`text-xs ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  Profile
                </div>
                <div className={`text-lg ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  →
                </div>
              </div>
            </div>
          </motion.div>

          {/* Best Posts */}
          <div className={`p-4 rounded-xl mb-6 ${
            isNightMode ? 'bg-gray-700/50' : 'bg-gray-50/50'
          }`}>
            <h3 className={`font-semibold mb-4 flex items-center ${
              isNightMode ? 'text-white' : 'text-gray-800'
            }`}>
              <Star className="w-5 h-5 mr-2 text-yellow-500" />
              Best Posts This Week
            </h3>

            <div className="space-y-3">
              {bestPosts.map((post, index) => (
                <motion.div
                  key={post.user}
                  className={`flex items-center space-x-3 justify-between p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                    isNightMode ? 'hover:bg-gray-600/50' : 'hover:bg-white/50'
                  }`}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold ${
                      index === 0 ? 'bg-yellow-500 text-white' :
                      index === 1 ? 'bg-gray-400 text-white' :
                      index === 2 ? 'bg-orange-500 text-white' :
                      'bg-gray-300 text-gray-700'
                    }`}>
                      #{index + 1}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className={`font-medium text-sm truncate ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                        {post.content}
                      </p>
                      <p className={`text-xs truncate ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {post.user} • {post.clan}
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <span className="text-xs font-semibold text-red-500">
                      ❤️ {post.likes}
                    </span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Upcoming Events */}
          <div className={`p-4 rounded-xl mb-6 ${
            isNightMode ? 'bg-gray-700/50' : 'bg-gray-50/50'
          }`}>
            <h3 className={`font-semibold mb-4 flex items-center ${
              isNightMode ? 'text-white' : 'text-gray-800'
            }`}>
              <Calendar className="w-5 h-5 mr-2 text-green-500" />
              Upcoming Events
            </h3>

            <div className="space-y-3">
              {upcomingEvents.map((item, index) => (
                <motion.div
                  key={index}
                  className={`flex items-center space-x-3 p-2 rounded-lg cursor-pointer transition-all duration-200 ${
                    isNightMode ? 'hover:bg-gray-600/50' : 'hover:bg-white/50'
                  }`}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className={`w-2 h-2 rounded-full animate-pulse ${
                    item.type === 'Convention' ? 'bg-purple-500' :
                    item.type === 'Movie Night' ? 'bg-red-500' :
                    item.type === 'Live Stream' ? 'bg-blue-500' :
                    'bg-green-500'
                  }`}></div>
                  <div className="flex-1 min-w-0">
                    <p className={`font-medium text-sm truncate ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                      {item.event}
                    </p>
                    <p className={`text-xs ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      {item.type} • {item.date}
                    </p>
                    <p className={`text-xs ${isNightMode ? 'text-purple-400' : 'text-purple-600'}`}>
                      📍 {item.location}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Guild Suggestions */}
          <div className={`p-4 rounded-xl mb-6 ${
            isNightMode ? 'bg-gray-700/50' : 'bg-gray-50/50'
          }`}>
            <h3 className={`font-semibold mb-4 flex items-center ${
              isNightMode ? 'text-white' : 'text-gray-800'
            }`}>
              <Users className="w-5 h-5 mr-2 text-blue-500" />
              Suggested Groups
            </h3>
            <p className={`text-xs mb-3 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
              Join groups with members from different clans
            </p>

            <div className="space-y-3">
              {[
                { name: 'Shonen Legends', members: '45.2K', avatar: '⚡' },
                { name: 'Manga Collectors United', members: '89.1K', avatar: '📚' },
                { name: 'Fan Art Masters', members: '23.7K', avatar: '🎨' },
                { name: 'Episode Discussion Hub', members: '67.5K', avatar: '�' }
              ].map((community, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white">
                      {community.avatar}
                    </div>
                    <div>
                      <p className={`font-medium text-sm ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                        {community.name}
                      </p>
                      <p className={`text-xs ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {community.members} members
                      </p>
                    </div>
                  </div>
                  <button className="text-purple-500 text-sm font-semibold">Join</button>
                </div>
              ))}
            </div>
          </div>

          {/* Footer */}
          <div className={`text-xs space-y-1 ${isNightMode ? 'text-gray-500' : 'text-gray-400'}`}>
            <p>About • Help • Privacy • Terms</p>
            <p>© 2024 AnimeVerse. Made with 💜 for anime fans</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
