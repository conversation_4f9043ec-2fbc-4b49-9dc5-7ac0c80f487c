import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaArrowLeft, FaSearch, FaFilter, FaStar, FaHeart, FaShoppingCart,
  FaUser, FaEye, FaTag, FaSort, FaTh, FaList, FaPlus, FaBell,
  FaExchangeAlt, FaCoins, FaCheckCircle, FaTrophy
} from 'react-icons/fa';
import { useTheme } from '../context/ThemeContext';
import { useNavigate } from 'react-router-dom';

const EdenMarket = () => {
  const { isNightMode } = useTheme();
  const navigate = useNavigate();
  
  const [activeTab, setActiveTab] = useState('browse'); // browse, selling, wishlist, trading
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState('grid'); // grid, list
  const [sortBy, setSortBy] = useState('newest'); // newest, price-low, price-high, rating
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Filter states
  const [filters, setFilters] = useState({
    category: 'all',
    priceRange: [0, 1000],
    condition: '',
    location: '',
    seller: ''
  });

  // Mock marketplace data
  const marketplaceItems = [
    {
      id: 1,
      title: 'Demon Slayer Tanjiro Figure',
      category: 'figures',
      price: 89.99,
      originalPrice: 120.00,
      condition: 'New',
      seller: {
        username: 'FigureCollector_Pro',
        rating: 4.9,
        sales: 156,
        clan: 'Demon Slayer',
        verified: true
      },
      images: ['🗡️'],
      description: 'Premium Tanjiro Kamado figure from Good Smile Company. Brand new in box with all accessories.',
      location: 'Tokyo, Japan',
      shipping: 'Free shipping worldwide',
      views: 234,
      likes: 45,
      postedDate: '2024-01-10',
      tags: ['demon-slayer', 'tanjiro', 'figure', 'good-smile'],
      inWishlist: false,
      priceHistory: [
        { date: '2024-01-10', price: 89.99 },
        { date: '2024-01-05', price: 95.00 },
        { date: '2023-12-20', price: 120.00 }
      ]
    },
    {
      id: 2,
      title: 'One Piece Manga Set Vol 1-50',
      category: 'manga',
      price: 299.99,
      originalPrice: 450.00,
      condition: 'Like New',
      seller: {
        username: 'MangaLibrary_Senpai',
        rating: 4.8,
        sales: 89,
        clan: 'One Piece',
        verified: true
      },
      images: ['📚'],
      description: 'Complete One Piece manga collection volumes 1-50. Excellent condition, minimal shelf wear.',
      location: 'California, USA',
      shipping: '$15 shipping',
      views: 567,
      likes: 123,
      postedDate: '2024-01-08',
      tags: ['one-piece', 'manga', 'collection', 'luffy'],
      inWishlist: true,
      priceHistory: [
        { date: '2024-01-08', price: 299.99 },
        { date: '2024-01-01', price: 320.00 }
      ]
    },
    {
      id: 3,
      title: 'Attack on Titan Poster Set',
      category: 'posters',
      price: 24.99,
      originalPrice: 35.00,
      condition: 'New',
      seller: {
        username: 'AnimeArt_Studio',
        rating: 4.7,
        sales: 234,
        clan: 'Attack on Titan',
        verified: false
      },
      images: ['🖼️'],
      description: 'Official Attack on Titan poster set featuring Eren, Mikasa, and Levi. High quality prints.',
      location: 'London, UK',
      shipping: '$8 shipping',
      views: 189,
      likes: 67,
      postedDate: '2024-01-12',
      tags: ['attack-on-titan', 'poster', 'eren', 'official'],
      inWishlist: false,
      priceHistory: [
        { date: '2024-01-12', price: 24.99 }
      ]
    },
    {
      id: 4,
      title: 'Naruto Headband Collection',
      category: 'accessories',
      price: 45.00,
      originalPrice: 60.00,
      condition: 'Used',
      seller: {
        username: 'NinjaGear_Shop',
        rating: 4.6,
        sales: 67,
        clan: 'Naruto',
        verified: true
      },
      images: ['🎭'],
      description: 'Authentic Naruto headband collection from different villages. Great for cosplay!',
      location: 'Osaka, Japan',
      shipping: 'Free shipping',
      views: 345,
      likes: 89,
      postedDate: '2024-01-09',
      tags: ['naruto', 'headband', 'cosplay', 'ninja'],
      inWishlist: true,
      priceHistory: [
        { date: '2024-01-09', price: 45.00 },
        { date: '2023-12-15', price: 50.00 }
      ]
    }
  ];

  // Mock trading offers
  const tradingOffers = [
    {
      id: 1,
      fromUser: 'TradeKing_2024',
      toUser: 'You',
      offering: ['Goku Figure', 'Dragon Ball Manga Vol 1-10'],
      requesting: ['Demon Slayer Figure Set'],
      status: 'pending',
      date: '2024-01-15',
      clan: 'Dragon Ball'
    },
    {
      id: 2,
      fromUser: 'You',
      toUser: 'CollectorSenpai',
      offering: ['One Piece Poster'],
      requesting: ['Luffy Nendoroid'],
      status: 'accepted',
      date: '2024-01-14',
      clan: 'One Piece'
    }
  ];

  // Mock wishlist
  const wishlistItems = marketplaceItems.filter(item => item.inWishlist);

  const categories = [
    { id: 'all', name: 'All Items', icon: '🛒', count: marketplaceItems.length },
    { id: 'figures', name: 'Figures', icon: '🎎', count: 1 },
    { id: 'manga', name: 'Manga', icon: '📚', count: 1 },
    { id: 'posters', name: 'Posters', icon: '🖼️', count: 1 },
    { id: 'accessories', name: 'Accessories', icon: '👑', count: 1 },
    { id: 'cards', name: 'Trading Cards', icon: '🃏', count: 0 },
    { id: 'clothing', name: 'Clothing', icon: '👕', count: 0 }
  ];

  const getConditionColor = (condition) => {
    switch (condition) {
      case 'New': return 'text-green-500';
      case 'Like New': return 'text-blue-500';
      case 'Used': return 'text-yellow-500';
      case 'Poor': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const filteredItems = marketplaceItems.filter(item => {
    if (searchQuery && !item.title.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    if (selectedCategory !== 'all' && item.category !== selectedCategory) {
      return false;
    }
    if (filters.condition && item.condition !== filters.condition) {
      return false;
    }
    return true;
  });

  const sortedItems = [...filteredItems].sort((a, b) => {
    switch (sortBy) {
      case 'price-low': return a.price - b.price;
      case 'price-high': return b.price - a.price;
      case 'rating': return b.seller.rating - a.seller.rating;
      case 'newest':
      default: return new Date(b.postedDate) - new Date(a.postedDate);
    }
  });

  return (
    // 🔧 SCROLLBAR PATTERN - Consistent with all pages
    <div className={`min-h-screen overflow-hidden transition-all duration-300 ${
      isNightMode 
        ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900' 
        : 'bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50'
    }`}>
      <div className="h-screen overflow-y-auto hide-scrollbar">
        
        {/* 🌸 Floating Sakura Petals */}
        <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-pink-300 rounded-full opacity-20"
              style={{
                left: `${Math.random() * 100}%`,
                top: `-10px`
              }}
              animate={{
                y: window.innerHeight + 50,
                x: [0, 30, -30, 0],
                rotate: 360,
                opacity: [0.2, 0.05, 0.2]
              }}
              transition={{
                duration: Math.random() * 3 + 12,
                repeat: Infinity,
                delay: Math.random() * 5,
                ease: "linear"
              }}
            />
          ))}
        </div>

        <div className="relative z-10 max-w-7xl mx-auto p-4">
          
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={() => navigate('/home')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                isNightMode ? 'text-gray-300 hover:text-white hover:bg-gray-700' : 'text-gray-600 hover:text-gray-800 hover:bg-white'
              }`}
            >
              <FaArrowLeft className="w-4 h-4" />
              <span>Back to Home</span>
            </button>
            
            <h1 className={`text-3xl font-bold flex items-center space-x-3 ${
              isNightMode ? 'text-white' : 'text-gray-800'
            }`}>
              <span>🛒</span>
              <span>Eden Market</span>
            </h1>

            <div className="flex items-center space-x-2">
              <button
                className={`p-2 rounded-lg transition-colors ${
                  isNightMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <FaBell className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className={`p-2 rounded-lg transition-colors ${
                  isNightMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {viewMode === 'grid' ? <FaList className="w-5 h-5" /> : <FaTh className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {/* Search and Filters */}
          <div className={`p-6 rounded-2xl mb-6 backdrop-blur-sm ${
            isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
          }`}>
            <div className="flex flex-col md:flex-row gap-4">
              
              {/* Search Bar */}
              <div className="flex-1 relative">
                <FaSearch className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${
                  isNightMode ? 'text-gray-400' : 'text-gray-500'
                }`} />
                <input
                  type="text"
                  placeholder="Search figures, manga, posters, accessories..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-colors ${
                    isNightMode
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500'
                      : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500'
                  } focus:outline-none focus:ring-0`}
                />
              </div>

              {/* Sort Dropdown */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className={`px-4 py-3 rounded-lg border transition-colors ${
                  isNightMode
                    ? 'bg-gray-700 border-gray-600 text-white focus:border-purple-500'
                    : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-purple-500'
                } focus:outline-none focus:ring-0`}
              >
                <option value="newest">Newest First</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="rating">Seller Rating</option>
              </select>

              {/* Filter Button */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center space-x-2 px-6 py-3 rounded-lg transition-colors ${
                  showFilters
                    ? 'bg-purple-500 text-white'
                    : isNightMode 
                      ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <FaFilter className="w-4 h-4" />
                <span>Filters</span>
              </button>

              {/* Sell Button */}
              <button
                className="flex items-center space-x-2 px-6 py-3 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:from-green-600 hover:to-emerald-600 transition-all"
              >
                <FaPlus className="w-4 h-4" />
                <span>Sell Item</span>
              </button>
            </div>
          </div>

          {/* Categories */}
          <div className={`p-4 rounded-2xl mb-6 backdrop-blur-sm ${
            isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
          }`}>
            <div className="flex space-x-2 overflow-x-auto hide-scrollbar pb-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-all ${
                    selectedCategory === category.id
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                      : isNightMode 
                        ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <span className="text-lg">{category.icon}</span>
                  <span className="font-medium">{category.name}</span>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    selectedCategory === category.id
                      ? 'bg-white/20 text-white'
                      : isNightMode ? 'bg-gray-600 text-gray-300' : 'bg-gray-200 text-gray-600'
                  }`}>
                    {category.count}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className={`flex space-x-1 mb-6 p-1 rounded-xl ${
            isNightMode ? 'bg-gray-800/50' : 'bg-white/50'
          }`}>
            {[
              { id: 'browse', label: 'Browse', icon: FaSearch },
              { id: 'wishlist', label: 'Wishlist', icon: FaHeart },
              { id: 'trading', label: 'Trading', icon: FaExchangeAlt },
              { id: 'selling', label: 'My Sales', icon: FaTag }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                    : isNightMode 
                      ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700/50' 
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span className="font-medium">{tab.label}</span>
                {tab.id === 'wishlist' && wishlistItems.length > 0 && (
                  <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                    {wishlistItems.length}
                  </span>
                )}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <AnimatePresence mode="wait">
            {activeTab === 'browse' && (
              <motion.div
                key="browse"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <MarketplaceGrid items={sortedItems} viewMode={viewMode} isNightMode={isNightMode} />
              </motion.div>
            )}

            {activeTab === 'wishlist' && (
              <motion.div
                key="wishlist"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="mb-6">
                  <h2 className={`text-2xl font-bold flex items-center space-x-2 ${
                    isNightMode ? 'text-white' : 'text-gray-800'
                  }`}>
                    <FaHeart className="text-pink-500" />
                    <span>Your Wishlist</span>
                  </h2>
                  <p className={`text-sm mt-1 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    Items you're interested in buying
                  </p>
                </div>
                {wishlistItems.length > 0 ? (
                  <MarketplaceGrid items={wishlistItems} viewMode={viewMode} isNightMode={isNightMode} />
                ) : (
                  <div className={`text-center py-12 rounded-xl ${
                    isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
                  }`}>
                    <FaHeart className={`w-16 h-16 mx-auto mb-4 ${isNightMode ? 'text-gray-600' : 'text-gray-300'}`} />
                    <p className={`text-lg mb-2 ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      Your wishlist is empty
                    </p>
                    <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      Browse items and click the heart icon to add them here
                    </p>
                  </div>
                )}
              </motion.div>
            )}

            {activeTab === 'trading' && (
              <motion.div
                key="trading"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="mb-6">
                  <h2 className={`text-2xl font-bold flex items-center space-x-2 ${
                    isNightMode ? 'text-white' : 'text-gray-800'
                  }`}>
                    <FaExchangeAlt className="text-blue-500" />
                    <span>Trading Hub</span>
                  </h2>
                  <p className={`text-sm mt-1 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    Trade items with other collectors
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Incoming Offers */}
                  <div className={`p-6 rounded-xl backdrop-blur-sm ${
                    isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
                  }`}>
                    <h3 className={`text-lg font-bold mb-4 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                      Incoming Offers
                    </h3>
                    <div className="space-y-4">
                      {tradingOffers.filter(offer => offer.toUser === 'You').map((offer) => (
                        <div key={offer.id} className={`p-4 rounded-lg border-2 ${
                          offer.status === 'pending' ? 'border-yellow-500' : 'border-green-500'
                        }`}>
                          <div className="flex items-center justify-between mb-2">
                            <span className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                              {offer.fromUser}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                              offer.status === 'pending'
                                ? 'bg-yellow-500 text-white'
                                : 'bg-green-500 text-white'
                            }`}>
                              {offer.status.toUpperCase()}
                            </span>
                          </div>
                          <div className={`text-sm mb-2 ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            <strong>Offering:</strong> {offer.offering.join(', ')}
                          </div>
                          <div className={`text-sm mb-3 ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            <strong>Requesting:</strong> {offer.requesting.join(', ')}
                          </div>
                          {offer.status === 'pending' && (
                            <div className="flex space-x-2">
                              <button className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600">
                                Accept
                              </button>
                              <button className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600">
                                Decline
                              </button>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Your Offers */}
                  <div className={`p-6 rounded-xl backdrop-blur-sm ${
                    isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
                  }`}>
                    <h3 className={`text-lg font-bold mb-4 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                      Your Offers
                    </h3>
                    <div className="space-y-4">
                      {tradingOffers.filter(offer => offer.fromUser === 'You').map((offer) => (
                        <div key={offer.id} className={`p-4 rounded-lg border-2 ${
                          offer.status === 'pending' ? 'border-blue-500' : 'border-green-500'
                        }`}>
                          <div className="flex items-center justify-between mb-2">
                            <span className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                              To: {offer.toUser}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                              offer.status === 'pending'
                                ? 'bg-blue-500 text-white'
                                : 'bg-green-500 text-white'
                            }`}>
                              {offer.status.toUpperCase()}
                            </span>
                          </div>
                          <div className={`text-sm mb-2 ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            <strong>You offered:</strong> {offer.offering.join(', ')}
                          </div>
                          <div className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            <strong>For:</strong> {offer.requesting.join(', ')}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {activeTab === 'selling' && (
              <motion.div
                key="selling"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="mb-6">
                  <h2 className={`text-2xl font-bold flex items-center space-x-2 ${
                    isNightMode ? 'text-white' : 'text-gray-800'
                  }`}>
                    <FaTag className="text-green-500" />
                    <span>Your Sales</span>
                  </h2>
                  <p className={`text-sm mt-1 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    Manage your listings and sales
                  </p>
                </div>

                <div className={`text-center py-12 rounded-xl backdrop-blur-sm ${
                  isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
                }`}>
                  <FaTag className={`w-16 h-16 mx-auto mb-4 ${isNightMode ? 'text-gray-600' : 'text-gray-300'}`} />
                  <p className={`text-lg mb-2 ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    You haven't listed any items yet
                  </p>
                  <p className={`text-sm mb-4 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    Start selling your anime merchandise to other collectors
                  </p>
                  <button className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg font-semibold hover:from-green-600 hover:to-emerald-600 transition-all">
                    List Your First Item
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

// Marketplace Grid Component
const MarketplaceGrid = ({ items, viewMode, isNightMode }) => {
  const getConditionColor = (condition) => {
    switch (condition) {
      case 'New': return 'text-green-500';
      case 'Like New': return 'text-blue-500';
      case 'Used': return 'text-yellow-500';
      case 'Poor': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  if (viewMode === 'list') {
    return (
      <div className="space-y-4">
        {items.map((item, index) => (
          <motion.div
            key={item.id}
            className={`p-6 rounded-xl backdrop-blur-sm transition-all hover:scale-[1.02] cursor-pointer ${
              isNightMode ? 'bg-gray-800/90 hover:bg-gray-700/90' : 'bg-white/90 hover:bg-gray-50/90'
            }`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <div className="flex items-start space-x-6">
              <div className="w-24 h-32 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-4xl shadow-lg">
                {item.images[0]}
              </div>

              <div className="flex-1">
                <div className="flex items-start justify-between mb-2">
                  <h3 className={`text-xl font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                    {item.title}
                  </h3>
                  <div className="text-right">
                    <div className={`text-2xl font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                      {formatPrice(item.price)}
                    </div>
                    {item.originalPrice > item.price && (
                      <div className={`text-sm line-through ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {formatPrice(item.originalPrice)}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-4 mb-3">
                  <span className={`text-sm font-semibold ${getConditionColor(item.condition)}`}>
                    {item.condition}
                  </span>
                  <span className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    {item.location}
                  </span>
                  <span className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    {item.shipping}
                  </span>
                </div>

                <div className="flex items-center space-x-4 mb-3">
                  <div className="flex items-center space-x-1">
                    <FaUser className={`w-4 h-4 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`} />
                    <span className={`text-sm font-medium ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                      {item.seller.username}
                    </span>
                    {item.seller.verified && (
                      <FaCheckCircle className="w-4 h-4 text-blue-500" />
                    )}
                  </div>
                  <div className="flex items-center space-x-1">
                    <FaStar className="w-4 h-4 text-yellow-500" />
                    <span className={`text-sm ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                      {item.seller.rating}
                    </span>
                    <span className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      ({item.seller.sales} sales)
                    </span>
                  </div>
                </div>

                <p className={`text-sm mb-4 ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  {item.description}
                </p>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <FaEye className={`w-4 h-4 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`} />
                      <span className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {item.views}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <FaHeart className={`w-4 h-4 ${item.inWishlist ? 'text-red-500' : isNightMode ? 'text-gray-400' : 'text-gray-500'}`} />
                      <span className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {item.likes}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button className={`p-2 rounded-lg transition-colors ${
                      item.inWishlist
                        ? 'text-red-500 bg-red-500/20'
                        : isNightMode ? 'text-gray-400 hover:text-red-500' : 'text-gray-500 hover:text-red-500'
                    }`}>
                      <FaHeart className="w-4 h-4" />
                    </button>
                    <button className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                      Buy Now
                    </button>
                    <button className="px-4 py-2 border border-blue-500 text-blue-500 rounded-lg hover:bg-blue-500 hover:text-white transition-colors">
                      Make Offer
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    );
  }

  // Grid View
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {items.map((item, index) => (
        <motion.div
          key={item.id}
          className={`p-4 rounded-xl backdrop-blur-sm transition-all hover:scale-105 cursor-pointer ${
            isNightMode ? 'bg-gray-800/90 hover:bg-gray-700/90' : 'bg-white/90 hover:bg-gray-50/90'
          }`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <div className="relative mb-4">
            <div className="w-full h-48 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-6xl shadow-lg">
              {item.images[0]}
            </div>
            <button className={`absolute top-2 right-2 p-2 rounded-full transition-colors ${
              item.inWishlist
                ? 'bg-red-500 text-white'
                : 'bg-white/80 text-gray-600 hover:bg-red-500 hover:text-white'
            }`}>
              <FaHeart className="w-4 h-4" />
            </button>
            {item.originalPrice > item.price && (
              <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                SALE
              </div>
            )}
          </div>

          <h3 className={`font-bold text-lg mb-2 line-clamp-2 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
            {item.title}
          </h3>

          <div className="flex items-center justify-between mb-2">
            <div className={`text-xl font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
              {formatPrice(item.price)}
            </div>
            <span className={`text-sm font-semibold ${getConditionColor(item.condition)}`}>
              {item.condition}
            </span>
          </div>

          {item.originalPrice > item.price && (
            <div className={`text-sm line-through mb-2 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
              {formatPrice(item.originalPrice)}
            </div>
          )}

          <div className="flex items-center space-x-2 mb-3">
            <div className="flex items-center space-x-1">
              <FaStar className="w-3 h-3 text-yellow-500" />
              <span className={`text-sm ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                {item.seller.rating}
              </span>
            </div>
            <span className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
              {item.seller.username}
            </span>
            {item.seller.verified && (
              <FaCheckCircle className="w-3 h-3 text-blue-500" />
            )}
          </div>

          <div className={`text-sm mb-3 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
            {item.location} • {item.shipping}
          </div>

          <div className="flex items-center space-x-2">
            <button className="flex-1 bg-purple-500 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-purple-600 transition-colors">
              Buy Now
            </button>
            <button className="px-3 py-2 border border-blue-500 text-blue-500 rounded-lg text-sm hover:bg-blue-500 hover:text-white transition-colors">
              Offer
            </button>
          </div>
        </motion.div>
      ))}
    </div>
  );
};

export default EdenMarket;
