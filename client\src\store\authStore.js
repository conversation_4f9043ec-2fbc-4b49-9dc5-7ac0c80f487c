import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      setUser: (user) => set({ user, isAuthenticated: !!user }),
      
      setToken: (token) => set({ token }),
      
      setLoading: (isLoading) => set({ isLoading }),
      
      setError: (error) => set({ error }),
      
      login: (userData, token) => set({
        user: userData,
        token,
        isAuthenticated: true,
        error: null
      }),
      
      logout: () => set({
        user: null,
        token: null,
        isAuthenticated: false,
        error: null
      }),
      
      clearError: () => set({ error: null }),
      
      // Update user profile
      updateUser: (updates) => set((state) => ({
        user: state.user ? { ...state.user, ...updates } : null
      })),
      
      // Check if user is authenticated
      checkAuth: () => {
        const { token, user } = get()
        return !!(token && user)
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
