const express = require('express');
const router = express.Router();
const animeController = require('../controllers/animeController');
const { apiRateLimiter } = require('../middleware/rateLimiter');
const { searchValidation, validate } = require('../utils/validator');

/**
 * @swagger
 * /api/anime/search:
 *   get:
 *     summary: Search anime using Jikan API
 *     tags: [Anime]
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         description: Search query (min 2 chars)
 *         schema:
 *           type: string
 *       - in: query
 *         name: page
 *         description: Page number
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         description: Results per page
 *         schema:
 *           type: integer
 *           default: 10
 *     responses:
 *       200:
 *         description: Anime search results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *                 results:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/AnimeResult'
 *       400:
 *         description: Invalid search query
 *       504:
 *         description: Jikan API timeout
 */
router.get(
  '/search',
  apiRateLimiter,
  searchValidation(),
  validate,
  animeController.searchAnime
);

module.exports = router;