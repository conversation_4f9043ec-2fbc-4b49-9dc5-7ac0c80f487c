const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const User = require('../models/user.model');
const RefreshToken = require('../models/RefreshToken');
const { 
  jwtSecret, 
  refreshSecret,
  accessToken,
  refreshToken,
  verifyOptions
} = require('../config/jwt');
const { logger } = require('../utils/logger');

const authController = {
  async register(req, res, next) {
    try {
      const { username, email, password } = req.body;
      
      const existingUser = await User.findByEmail(email);
      if (existingUser) {
        return res.status(400).json({ message: 'User already exists' });
      }

      const newUser = await User.create({ username, email, password });
      const { accessToken, refreshToken } = await generateTokens(newUser.id);

      logger.info(`New user registered: ${newUser.id}`);
      res.status(201).json({ 
        user: { id: newUser.id, username: newUser.username },
        accessToken,
        refreshToken
      });
    } catch (err) {
      next(err);
    }
  },

  async login(req, res, next) {
    try {
      const { email, password } = req.body;
      const user = await User.findByEmail(email);
      
      if (!user || !(await bcrypt.compare(password, user.password))) {
        logger.warn(`Failed login attempt for email: ${email}`);
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      const { accessToken, refreshToken } = await generateTokens(user.id);
      logger.info(`User logged in: ${user.id}`);
      
      res.json({ 
        user: { id: user.id, username: user.username },
        accessToken,
        refreshToken
      });
    } catch (err) {
      next(err);
    }
  }
};

async function generateTokens(userId) {
  const accessToken = jwt.sign(
    { userId }, 
    process.env.JWT_SECRET, 
    { expiresIn: '15m' }
  );
  
  const refreshToken = jwt.sign(
    { userId }, 
    process.env.REFRESH_SECRET, 
    { expiresIn: '7d' }
  );
  
  await RefreshToken.create(
    userId, 
    refreshToken, 
    new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
  );
  
  return { accessToken, refreshToken };
}

module.exports = authController;