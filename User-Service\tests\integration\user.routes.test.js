import request from 'supertest';
import { app } from '../../app.js';
import User from '../../models/user.model.js';
import jwt from 'jsonwebtoken';

describe('User Routes', () => {
  let server;
  let authToken;
  let userId;

  beforeAll(async () => {
    server = app.listen(0); // Random port
  });

  beforeEach(async () => {
    const user = await User.create({
      username: `testuser_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      password: 'password123'
    });

    userId = user._id.toString();
    authToken = jwt.sign(
      { id: userId, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );
  });

  afterAll(async () => {
    await server.close();
  });

  describe('GET /api/users/:id', () => {
    it('should get user by ID', async () => {
      const res = await request(app)
        .get(`/api/users/${userId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(res.status).toBe(200);
      expect(res.body._id).toBe(userId);
      expect(res.body.password).toBeUndefined();
    });

    it('should return 404 for non-existent user', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const res = await request(app)
        .get(`/api/users/${fakeId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(res.status).toBe(404);
    });
  });

  describe('PUT /api/users/profile', () => {
    it('should update user profile', async () => {
      const res = await request(app)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          bio: 'New bio text',
          favoriteAnime: ['One Piece', 'Attack on Titan']
        });

      expect(res.status).toBe(200);
      expect(res.body.bio).toBe('New bio text');
      expect(res.body.favoriteAnime).toContain('One Piece');
    });

    it('should not allow updating protected fields', async () => {
      const res = await request(app)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          email: '<EMAIL>', // Should not be updatable
          isPremium: true // Should not be updatable this way
        });

      expect(res.status).toBe(200);
      expect(res.body.email).toBe('<EMAIL>'); // Unchanged
      expect(res.body.isPremium).toBe(false); // Unchanged
    });
  });

  describe('POST /api/users/follow/:id', () => {
    it('should follow another user', async () => {
      const userToFollow = await User.create({
        username: 'user2',
        email: '<EMAIL>',
        password: 'password123'
      });

      const res = await request(app)
        .post(`/api/users/follow/${userToFollow._id}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(res.status).toBe(200);
      expect(res.body.success).toBe(true);

      // Verify the follow relationship
      const updatedUser = await User.findById(userId);
      const updatedTarget = await User.findById(userToFollow._id);
      
      expect(updatedUser?.following).toContainEqual(userToFollow._id);
      expect(updatedTarget?.followers).toContainEqual(userId);
    });
  });
});