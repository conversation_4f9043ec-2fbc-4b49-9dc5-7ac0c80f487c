const express = require('express');
const router = express.Router();
const authMiddleware = require('../middleware/auth.middleware');
const UserService = require('../services/user.service');

// @route   GET api/users/:id
// @desc    Get user by ID
// @access  Public
router.get('/:id', async (req, res, next) => {
  try {
    const user = await UserService.getUserById(req.params.id);
    res.json(user);
  } catch (error) {
    next(error);
  }
});

// @route   PUT api/users/profile
// @desc    Update user profile
// @access  Private
router.put('/profile', authMiddleware, async (req, res, next) => {
  try {
    const user = await UserService.updateUserProfile(req.user.id, req.body);
    res.json(user);
  } catch (error) {
    next(error);
  }
});

// @route   POST api/users/follow/:id
// @desc    Follow a user
// @access  Private
router.post('/follow/:id', authMiddleware, async (req, res, next) => {
  try {
    const result = await UserService.followUser(req.user.id, req.params.id);
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// @route   POST api/users/unfollow/:id
// @desc    Unfollow a user
// @access  Private
router.post('/unfollow/:id', authMiddleware, async (req, res, next) => {
  try {
    const result = await UserService.unfollowUser(req.user.id, req.params.id);
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// @route   GET api/users/search?q=
// @desc    Search users
// @access  Public
router.get('/search', async (req, res, next) => {
  try {
    const users = await UserService.searchUsers(req.query.q);
    res.json(users);
  } catch (error) {
    next(error);
  }
});

module.exports = router;