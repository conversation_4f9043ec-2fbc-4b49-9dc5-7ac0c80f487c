import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FaArrowLeft, FaSearch, FaPlus, FaPaperPlane, FaSmile, FaPaperclip,
  FaUsers, FaUser, FaCircle, FaPhone, FaVideo, FaEllipsisV,
  FaMicrophone, FaImage, FaFile, FaHeart, FaReply
} from 'react-icons/fa';
import { useTheme } from '../context/ThemeContext';
import { useNavigate } from 'react-router-dom';

const SenseiChat = () => {
  const { isNightMode } = useTheme();
  const navigate = useNavigate();
  const messagesEndRef = useRef(null);
  
  const [activeTab, setActiveTab] = useState('direct'); // direct, groups, clan
  const [selectedChat, setSelectedChat] = useState(null);
  const [messageInput, setMessageInput] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  // Mock chat data
  const directChats = [
    {
      id: 1,
      type: 'direct',
      user: {
        username: 'AnimeKing_2024',
        avatar: '👑',
        clan: 'Naruto',
        isOnline: true,
        lastSeen: null
      },
      lastMessage: {
        content: 'Did you see the latest Naruto episode? 🔥',
        timestamp: '2024-01-15T14:30:00Z',
        sender: 'AnimeKing_2024',
        isRead: false
      },
      unreadCount: 2
    },
    {
      id: 2,
      type: 'direct',
      user: {
        username: 'MangaCollector_Pro',
        avatar: '📚',
        clan: 'One Piece',
        isOnline: false,
        lastSeen: '2024-01-15T12:00:00Z'
      },
      lastMessage: {
        content: 'Thanks for the manga recommendation!',
        timestamp: '2024-01-15T10:15:00Z',
        sender: 'You',
        isRead: true
      },
      unreadCount: 0
    }
  ];

  const groupChats = [
    {
      id: 3,
      type: 'group',
      name: 'Shonen Legends',
      avatar: '⚡',
      members: 156,
      description: 'Discussing the best shonen anime of all time',
      lastMessage: {
        content: 'Who else is hyped for the new season?',
        timestamp: '2024-01-15T15:45:00Z',
        sender: 'DragonBallFan',
        isRead: true
      },
      unreadCount: 5
    },
    {
      id: 4,
      type: 'group',
      name: 'Manga Readers United',
      avatar: '📖',
      members: 89,
      description: 'Spoiler-free manga discussions',
      lastMessage: {
        content: 'New chapter was incredible! 😱',
        timestamp: '2024-01-15T13:20:00Z',
        sender: 'MangaSenpai',
        isRead: false
      },
      unreadCount: 12
    }
  ];

  const clanChats = [
    {
      id: 5,
      type: 'clan',
      name: 'Naruto Clan - General',
      avatar: '🍥',
      members: 234,
      description: 'Main discussion for Naruto clan members',
      lastMessage: {
        content: 'Clan tournament starts tomorrow!',
        timestamp: '2024-01-15T16:00:00Z',
        sender: 'NarutoHokage',
        isRead: true
      },
      unreadCount: 3
    }
  ];

  // Mock messages for selected chat
  const mockMessages = [
    {
      id: 1,
      sender: 'AnimeKing_2024',
      content: 'Hey! Did you watch the latest episode?',
      timestamp: '2024-01-15T14:25:00Z',
      type: 'text',
      isOwn: false,
      reactions: [{ emoji: '👍', count: 2, users: ['You', 'OtherUser'] }]
    },
    {
      id: 2,
      sender: 'You',
      content: 'Yes! It was amazing! The animation quality was incredible 🔥',
      timestamp: '2024-01-15T14:26:00Z',
      type: 'text',
      isOwn: true,
      reactions: []
    },
    {
      id: 3,
      sender: 'AnimeKing_2024',
      content: 'I know right! That fight scene gave me chills',
      timestamp: '2024-01-15T14:27:00Z',
      type: 'text',
      isOwn: false,
      reactions: [{ emoji: '🔥', count: 1, users: ['You'] }]
    },
    {
      id: 4,
      sender: 'You',
      content: 'Check out this fan art I found!',
      timestamp: '2024-01-15T14:28:00Z',
      type: 'image',
      imageUrl: '🎨',
      isOwn: true,
      reactions: [{ emoji: '❤️', count: 3, users: ['AnimeKing_2024', 'User1', 'User2'] }]
    }
  ];

  const allChats = [...directChats, ...groupChats, ...clanChats];
  const filteredChats = allChats.filter(chat => {
    if (activeTab === 'direct' && chat.type !== 'direct') return false;
    if (activeTab === 'groups' && chat.type !== 'group') return false;
    if (activeTab === 'clan' && chat.type !== 'clan') return false;
    
    if (searchQuery) {
      const searchTerm = searchQuery.toLowerCase();
      if (chat.type === 'direct') {
        return chat.user.username.toLowerCase().includes(searchTerm);
      } else {
        return chat.name.toLowerCase().includes(searchTerm);
      }
    }
    return true;
  });

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (messageInput.trim() && selectedChat) {
      console.log('Sending message:', messageInput);
      setMessageInput('');
      // Handle message sending
    }
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatLastSeen = (timestamp) => {
    const now = new Date();
    const lastSeen = new Date(timestamp);
    const diffInMinutes = Math.floor((now - lastSeen) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [selectedChat]);

  return (
    // 🔧 SCROLLBAR PATTERN - Consistent with all pages
    <div className={`min-h-screen overflow-hidden transition-all duration-300 ${
      isNightMode 
        ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900' 
        : 'bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50'
    }`}>
      <div className="h-screen overflow-y-auto hide-scrollbar">
        
        {/* 🌸 Floating Sakura Petals */}
        <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-pink-300 rounded-full opacity-20"
              style={{
                left: `${Math.random() * 100}%`,
                top: `-10px`
              }}
              animate={{
                y: window.innerHeight + 50,
                x: [0, 30, -30, 0],
                rotate: 360,
                opacity: [0.2, 0.05, 0.2]
              }}
              transition={{
                duration: Math.random() * 3 + 12,
                repeat: Infinity,
                delay: Math.random() * 5,
                ease: "linear"
              }}
            />
          ))}
        </div>

        <div className="relative z-10 h-full flex">
          
          {/* Sidebar - Chat List */}
          <div className={`w-80 border-r flex flex-col ${
            isNightMode ? 'bg-gray-800/90 border-gray-700' : 'bg-white/90 border-gray-200'
          }`}>
            
            {/* Header */}
            <div className="p-4 border-b border-gray-300">
              <div className="flex items-center justify-between mb-4">
                <button
                  onClick={() => navigate('/home')}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
                    isNightMode ? 'text-gray-300 hover:text-white hover:bg-gray-700' : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                  }`}
                >
                  <FaArrowLeft className="w-4 h-4" />
                  <span>Back</span>
                </button>
                
                <h1 className={`text-xl font-bold flex items-center space-x-2 ${
                  isNightMode ? 'text-white' : 'text-gray-800'
                }`}>
                  <span>💬</span>
                  <span>Sensei Chat</span>
                </h1>

                <button
                  className={`p-2 rounded-lg transition-colors ${
                    isNightMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <FaPlus className="w-4 h-4" />
                </button>
              </div>

              {/* Search */}
              <div className="relative">
                <FaSearch className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${
                  isNightMode ? 'text-gray-400' : 'text-gray-500'
                }`} />
                <input
                  type="text"
                  placeholder="Search conversations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`w-full pl-10 pr-4 py-2 rounded-lg border transition-colors ${
                    isNightMode
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500'
                      : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500'
                  } focus:outline-none focus:ring-0`}
                />
              </div>
            </div>

            {/* Navigation Tabs */}
            <div className={`flex p-2 ${
              isNightMode ? 'bg-gray-800/50' : 'bg-gray-50/50'
            }`}>
              {[
                { id: 'direct', label: 'Direct', icon: FaUser },
                { id: 'groups', label: 'Groups', icon: FaUsers },
                { id: 'clan', label: 'Clan', icon: FaCircle }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 flex items-center justify-center space-x-1 py-2 px-3 rounded-lg transition-all text-sm ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                      : isNightMode 
                        ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700/50' 
                        : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
                  }`}
                >
                  <tab.icon className="w-3 h-3" />
                  <span>{tab.label}</span>
                  {tab.id === 'direct' && directChats.reduce((sum, chat) => sum + chat.unreadCount, 0) > 0 && (
                    <span className="bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                      {directChats.reduce((sum, chat) => sum + chat.unreadCount, 0)}
                    </span>
                  )}
                  {tab.id === 'groups' && groupChats.reduce((sum, chat) => sum + chat.unreadCount, 0) > 0 && (
                    <span className="bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                      {groupChats.reduce((sum, chat) => sum + chat.unreadCount, 0)}
                    </span>
                  )}
                </button>
              ))}
            </div>

            {/* Chat List */}
            <div className="flex-1 overflow-y-auto hide-scrollbar">
              {filteredChats.map((chat) => (
                <div
                  key={chat.id}
                  onClick={() => setSelectedChat(chat)}
                  className={`p-4 border-b cursor-pointer transition-colors ${
                    selectedChat?.id === chat.id
                      ? isNightMode ? 'bg-purple-500/20 border-purple-500/30' : 'bg-purple-50 border-purple-200'
                      : isNightMode 
                        ? 'border-gray-700 hover:bg-gray-700/50' 
                        : 'border-gray-100 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className="relative">
                      <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-xl">
                        {chat.type === 'direct' ? chat.user.avatar : chat.avatar}
                      </div>
                      {chat.type === 'direct' && chat.user.isOnline && (
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
                      )}
                      {chat.unreadCount > 0 && (
                        <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full min-w-[20px] text-center">
                          {chat.unreadCount}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className={`font-semibold truncate ${
                          isNightMode ? 'text-white' : 'text-gray-800'
                        }`}>
                          {chat.type === 'direct' ? chat.user.username : chat.name}
                        </h3>
                        <span className={`text-xs ${
                          isNightMode ? 'text-gray-400' : 'text-gray-500'
                        }`}>
                          {formatTime(chat.lastMessage.timestamp)}
                        </span>
                      </div>
                      
                      {chat.type !== 'direct' && (
                        <div className={`text-xs mb-1 ${
                          isNightMode ? 'text-gray-400' : 'text-gray-500'
                        }`}>
                          {chat.members} members
                        </div>
                      )}
                      
                      <p className={`text-sm truncate ${
                        chat.lastMessage.isRead 
                          ? isNightMode ? 'text-gray-400' : 'text-gray-500'
                          : isNightMode ? 'text-gray-200' : 'text-gray-700'
                      }`}>
                        {chat.lastMessage.sender === 'You' ? 'You: ' : ''}
                        {chat.lastMessage.content}
                      </p>
                      
                      {chat.type === 'direct' && !chat.user.isOnline && chat.user.lastSeen && (
                        <p className={`text-xs mt-1 ${
                          isNightMode ? 'text-gray-500' : 'text-gray-400'
                        }`}>
                          Last seen {formatLastSeen(chat.user.lastSeen)}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Main Chat Area */}
          <div className="flex-1 flex flex-col">
            {selectedChat ? (
              <>
                {/* Chat Header */}
                <div className={`p-4 border-b flex items-center justify-between ${
                  isNightMode ? 'bg-gray-800/90 border-gray-700' : 'bg-white/90 border-gray-200'
                }`}>
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-lg">
                        {selectedChat.type === 'direct' ? selectedChat.user.avatar : selectedChat.avatar}
                      </div>
                      {selectedChat.type === 'direct' && selectedChat.user.isOnline && (
                        <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                      )}
                    </div>
                    
                    <div>
                      <h2 className={`font-semibold ${
                        isNightMode ? 'text-white' : 'text-gray-800'
                      }`}>
                        {selectedChat.type === 'direct' ? selectedChat.user.username : selectedChat.name}
                      </h2>
                      <p className={`text-sm ${
                        isNightMode ? 'text-gray-400' : 'text-gray-500'
                      }`}>
                        {selectedChat.type === 'direct' 
                          ? selectedChat.user.isOnline ? 'Online' : `Last seen ${formatLastSeen(selectedChat.user.lastSeen)}`
                          : `${selectedChat.members} members`
                        }
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {selectedChat.type === 'direct' && (
                      <>
                        <button className={`p-2 rounded-lg transition-colors ${
                          isNightMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                        }`}>
                          <FaPhone className="w-4 h-4" />
                        </button>
                        <button className={`p-2 rounded-lg transition-colors ${
                          isNightMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                        }`}>
                          <FaVideo className="w-4 h-4" />
                        </button>
                      </>
                    )}
                    <button className={`p-2 rounded-lg transition-colors ${
                      isNightMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                    }`}>
                      <FaEllipsisV className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Messages Area */}
                <div className={`flex-1 overflow-y-auto hide-scrollbar p-4 space-y-4 ${
                  isNightMode ? 'bg-gray-900/50' : 'bg-gray-50/50'
                }`}>
                  {mockMessages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.isOwn ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`max-w-xs lg:max-w-md ${message.isOwn ? 'order-2' : 'order-1'}`}>
                        {!message.isOwn && (
                          <p className={`text-xs mb-1 ${
                            isNightMode ? 'text-gray-400' : 'text-gray-500'
                          }`}>
                            {message.sender}
                          </p>
                        )}
                        
                        <div className={`p-3 rounded-2xl ${
                          message.isOwn
                            ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                            : isNightMode ? 'bg-gray-700 text-white' : 'bg-white text-gray-800'
                        } shadow-sm`}>
                          {message.type === 'text' ? (
                            <p className="text-sm">{message.content}</p>
                          ) : message.type === 'image' ? (
                            <div>
                              <div className="w-48 h-32 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center text-4xl mb-2">
                                {message.imageUrl}
                              </div>
                              <p className="text-sm">{message.content}</p>
                            </div>
                          ) : null}
                        </div>
                        
                        <div className="flex items-center justify-between mt-1">
                          <span className={`text-xs ${
                            isNightMode ? 'text-gray-500' : 'text-gray-400'
                          }`}>
                            {formatTime(message.timestamp)}
                          </span>
                          
                          {message.reactions.length > 0 && (
                            <div className="flex items-center space-x-1">
                              {message.reactions.map((reaction, index) => (
                                <div
                                  key={index}
                                  className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
                                    isNightMode ? 'bg-gray-600' : 'bg-gray-200'
                                  }`}
                                >
                                  <span>{reaction.emoji}</span>
                                  <span>{reaction.count}</span>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>

                {/* Message Input */}
                <div className={`p-4 border-t ${
                  isNightMode ? 'bg-gray-800/90 border-gray-700' : 'bg-white/90 border-gray-200'
                }`}>
                  <form onSubmit={handleSendMessage} className="flex items-end space-x-2">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <button
                          type="button"
                          className={`p-2 rounded-lg transition-colors ${
                            isNightMode ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          <FaPaperclip className="w-4 h-4" />
                        </button>
                        <button
                          type="button"
                          className={`p-2 rounded-lg transition-colors ${
                            isNightMode ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          <FaImage className="w-4 h-4" />
                        </button>
                        <button
                          type="button"
                          className={`p-2 rounded-lg transition-colors ${
                            isNightMode ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          <FaMicrophone className="w-4 h-4" />
                        </button>
                      </div>
                      
                      <div className="relative">
                        <input
                          type="text"
                          value={messageInput}
                          onChange={(e) => setMessageInput(e.target.value)}
                          placeholder="Type a message..."
                          className={`w-full px-4 py-3 pr-12 rounded-2xl border transition-colors ${
                            isNightMode
                              ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500'
                              : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500'
                          } focus:outline-none focus:ring-0`}
                        />
                        <button
                          type="button"
                          onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                          className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded transition-colors ${
                            isNightMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
                          }`}
                        >
                          <FaSmile className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                    
                    <button
                      type="submit"
                      disabled={!messageInput.trim()}
                      className={`p-3 rounded-2xl transition-all ${
                        messageInput.trim()
                          ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600'
                          : isNightMode ? 'bg-gray-700 text-gray-500' : 'bg-gray-200 text-gray-400'
                      }`}
                    >
                      <FaPaperPlane className="w-4 h-4" />
                    </button>
                  </form>
                </div>
              </>
            ) : (
              /* No Chat Selected */
              <div className={`flex-1 flex items-center justify-center ${
                isNightMode ? 'bg-gray-900/50' : 'bg-gray-50/50'
              }`}>
                <div className="text-center">
                  <div className="w-24 h-24 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-4xl mx-auto mb-4">
                    💬
                  </div>
                  <h2 className={`text-xl font-bold mb-2 ${
                    isNightMode ? 'text-white' : 'text-gray-800'
                  }`}>
                    Welcome to Sensei Chat
                  </h2>
                  <p className={`${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    Select a conversation to start messaging
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SenseiChat;
