import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

dotenv.config();

// Password hashing utilities
export const hashPassword = async (password) => {
  const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
  return await bcrypt.hash(password, saltRounds);
};

export const comparePassword = async (password, hashedPassword) => {
  return await bcrypt.compare(password, hashedPassword);
};

// JWT utilities
export const generateToken = (payload) => {
  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  });
};

export const verifyToken = (token) => {
  return jwt.verify(token, process.env.JWT_SECRET);
};

// Response utilities
export const successResponse = (res, data, message = 'Success', statusCode = 200) => {
  return res.status(statusCode).json({
    status: 'success',
    message,
    data
  });
};

export const errorResponse = (res, message = 'Something went wrong', statusCode = 500, errors = null) => {
  const response = {
    status: 'error',
    message
  };
  
  if (errors) {
    response.errors = errors;
  }
  
  return res.status(statusCode).json(response);
};

// Validation utilities
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password) => {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
};

// Pagination utilities
export const getPagination = (page, size) => {
  const limit = size ? +size : 10;
  const offset = page ? page * limit : 0;
  
  return { limit, offset };
};

export const getPagingData = (data, page, limit) => {
  const { count: totalItems, rows: items } = data;
  const currentPage = page ? +page : 0;
  const totalPages = Math.ceil(totalItems / limit);
  
  return {
    totalItems,
    items,
    totalPages,
    currentPage,
    hasNextPage: currentPage < totalPages - 1,
    hasPrevPage: currentPage > 0
  };
};

// File upload utilities
export const getFileExtension = (filename) => {
  return filename.split('.').pop().toLowerCase();
};

export const isValidImageFile = (filename) => {
  const validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
  const extension = getFileExtension(filename);
  return validExtensions.includes(extension);
};

export const isValidVideoFile = (filename) => {
  const validExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
  const extension = getFileExtension(filename);
  return validExtensions.includes(extension);
};

// Date utilities
export const formatDate = (date) => {
  return new Date(date).toISOString();
};

export const isDateInPast = (date) => {
  return new Date(date) < new Date();
};

// Anime community utilities
export const getCommunityTheme = (communityName) => {
  const themes = {
    'one-piece': { primary: '#FF6B35', secondary: '#F7931E' },
    'naruto': { primary: '#FF8C00', secondary: '#FFD700' },
    'attack-titan': { primary: '#2E8B57', secondary: '#32CD32' },
    'demon-slayer': { primary: '#8A2BE2', secondary: '#FF69B4' },
    'my-hero-academia': { primary: '#1E90FF', secondary: '#00BFFF' },
    'dragon-ball': { primary: '#FF4500', secondary: '#FFA500' }
  };
  
  return themes[communityName] || { primary: '#6366f1', secondary: '#ec4899' };
};
