const jwt = require('jsonwebtoken');
const User = require('../models/user.model');
const { JWT_SECRET, JWT_EXPIRE } = require('../config/jwt');

class AuthService {
  static async registerUser(userData) {
    try {
      // Check if user already exists
      const existingUser = await User.findOne({ 
        $or: [{ email: userData.email }, { username: userData.username }] 
      });
      
      if (existingUser) {
        throw new Error('User already exists with this email or username');
      }
      
      const user = new User(userData);
      await user.save();
      
      // Generate token
      const token = this.generateToken(user);
      
      return { user, token };
    } catch (error) {
      throw error;
    }
  }
  
  static async loginUser(email, password) {
    try {
      const user = await User.findOne({ email });
      
      if (!user) {
        throw new Error('Invalid credentials');
      }
      
      const isMatch = await user.comparePassword(password);
      
      if (!isMatch) {
        throw new Error('Invalid credentials');
      }
      
      // Generate token
      const token = this.generateToken(user);
      
      return { user, token };
    } catch (error) {
      throw error;
    }
  }
  
  static generateToken(user) {
    const payload = {
      id: user._id,
      username: user.username,
      email: user.email,
      isPremium: user.isPremium
    };
    
    return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRE });
  }
  
  static async verifyToken(token) {
    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      const user = await User.findById(decoded.id).select('-password');
      
      if (!user) {
        throw new Error('User not found');
      }
      
      return user;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = AuthService;