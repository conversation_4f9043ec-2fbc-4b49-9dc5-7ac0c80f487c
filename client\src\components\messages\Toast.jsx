import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaCheckCircle, FaExclamationTriangle, FaInfoCircle, FaTimes } from 'react-icons/fa';

const Toast = ({ message, type = 'info', duration = 3000, onClose }) => {
  useEffect(() => {
    if (message && onClose) {
      const timer = setTimeout(() => {
        onClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [message, duration, onClose]);

  if (!message) return null;

  const getToastConfig = () => {
    switch (type) {
      case 'success':
        return {
          icon: <FaCheckCircle className="text-green-500" />,
          bgColor: 'bg-green-50 dark:bg-green-900',
          borderColor: 'border-green-200 dark:border-green-700',
          textColor: 'text-green-800 dark:text-green-200'
        };
      case 'error':
        return {
          icon: <FaExclamationTriangle className="text-red-500" />,
          bgColor: 'bg-red-50 dark:bg-red-900',
          borderColor: 'border-red-200 dark:border-red-700',
          textColor: 'text-red-800 dark:text-red-200'
        };
      case 'warning':
        return {
          icon: <FaExclamationTriangle className="text-yellow-500" />,
          bgColor: 'bg-yellow-50 dark:bg-yellow-900',
          borderColor: 'border-yellow-200 dark:border-yellow-700',
          textColor: 'text-yellow-800 dark:text-yellow-200'
        };
      default:
        return {
          icon: <FaInfoCircle className="text-blue-500" />,
          bgColor: 'bg-blue-50 dark:bg-blue-900',
          borderColor: 'border-blue-200 dark:border-blue-700',
          textColor: 'text-blue-800 dark:text-blue-200'
        };
    }
  };

  const config = getToastConfig();

  return (
    <AnimatePresence>
      <motion.div
        className={`
          fixed top-4 right-4 z-50 max-w-sm w-full
          ${config.bgColor} ${config.borderColor} ${config.textColor}
          border rounded-lg shadow-lg p-4
        `}
        initial={{ opacity: 0, x: 100, scale: 0.8 }}
        animate={{ opacity: 1, x: 0, scale: 1 }}
        exit={{ opacity: 0, x: 100, scale: 0.8 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      >
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-3 mt-0.5">
            {config.icon}
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium">{message}</p>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className={`
                flex-shrink-0 ml-3 text-gray-400 hover:text-gray-600 
                dark:text-gray-500 dark:hover:text-gray-300
                transition-colors duration-200
              `}
            >
              <FaTimes className="w-4 h-4" />
            </button>
          )}
        </div>
        
        {/* Progress bar */}
        <motion.div
          className={`
            absolute bottom-0 left-0 h-1 rounded-b-lg
            ${type === 'success' ? 'bg-green-500' : 
              type === 'error' ? 'bg-red-500' : 
              type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'}
          `}
          initial={{ width: '100%' }}
          animate={{ width: '0%' }}
          transition={{ duration: duration / 1000, ease: "linear" }}
        />
      </motion.div>
    </AnimatePresence>
  );
};

export default Toast;
