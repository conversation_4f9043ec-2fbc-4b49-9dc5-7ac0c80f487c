const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const AuthService = require('../services/auth.service');
const validate = require('../utils/validator');

// @route   POST api/auth/register
// @desc    Register a new user
// @access  Public
router.post(
  '/register',
  [
    check('username', 'Username is required').not().isEmpty(),
    check('email', 'Please include a valid email').isEmail(),
    check('password', 'Please enter a password with 6 or more characters').isLength({ min: 6 })
  ],
  validate,
  async (req, res, next) => {
    try {
      const { user, token } = await AuthService.registerUser(req.body);
      res.status(201).json({ success: true, token, user });
    } catch (error) {
      next(error);
    }
  }
);

// @route   POST api/auth/login
// @desc    Login user
// @access  Public
router.post(
  '/login',
  [
    check('email', 'Please include a valid email').isEmail(),
    check('password', 'Password is required').exists()
  ],
  validate,
  async (req, res, next) => {
    try {
      const { email, password } = req.body;
      const { user, token } = await AuthService.loginUser(email, password);
      res.json({ success: true, token, user });
    } catch (error) {
      next(error);
    }
  }
);

// @route   GET api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', async (req, res, next) => {
  try {
    // This assumes you have an auth middleware that sets req.user
    const user = await AuthService.verifyToken(req.headers.authorization.split(' ')[1]);
    res.json(user);
  } catch (error) {
    next(error);
  }
});

module.exports = router;