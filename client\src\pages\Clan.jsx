import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaArrowLeft, FaGamepad, FaTrophy, FaFire, FaUsers, FaComments,
  FaExchangeAlt, FaCrown, FaStar, FaHome, FaCoins, FaExclamationTriangle
} from 'react-icons/fa';
import { useTheme } from '../context/ThemeContext';
import { useNavigate } from 'react-router-dom';

const Clan = () => {
  const { isNightMode } = useTheme();
  const navigate = useNavigate();
  
  // Mock user data
  const [user, setUser] = useState({
    username: 'AnimeOtaku_2024',
    clan: 'Naru<PERSON>',
    rank: 'Jonin',
    level: 42,
    points: 2450,
    clanJoinDate: '2024-01-15',
    clanContributions: 156
  });

  const [activeTab, setActiveTab] = useState('overview'); // overview, activities, members, chat
  const [showSwitchModal, setShowSwitchModal] = useState(false);

  // Clan configurations
  const clans = {
    '<PERSON>ru<PERSON>': {
      name: '<PERSON><PERSON><PERSON>',
      icon: '🍥',
      color: 'orange',
      ranks: ['Genin', 'Chunin', 'Jonin', 'Kage'],
      description: 'The path of the ninja - perseverance and never giving up!',
      members: 1247,
      level: 'S-Rank Village',
      motto: 'Believe it! Never give up on your dreams!'
    },
    'One Piece': {
      name: 'One Piece',
      icon: '🏴‍☠️',
      color: 'blue',
      ranks: ['Cabin Boy', 'Pirate', 'Captain', 'Admiral'],
      description: 'Set sail for adventure and find the ultimate treasure!',
      members: 2156,
      level: 'Legendary Crew',
      motto: 'The sea calls to those who dare to dream!'
    },
    'Attack on Titan': {
      name: 'Attack on Titan',
      icon: '⚔️',
      color: 'green',
      ranks: ['Cadet', 'Soldier', 'Squad Leader', 'Commander'],
      description: 'Humanity\'s last hope against the titans!',
      members: 987,
      level: 'Elite Corps',
      motto: 'Dedicate your hearts to humanity!'
    },
    'Demon Slayer': {
      name: 'Demon Slayer',
      icon: '🗡️',
      color: 'purple',
      ranks: ['Mizunoto', 'Kinoe', 'Hashira', 'Master'],
      description: 'Protect humanity from the demons of the night!',
      members: 1543,
      level: 'Sacred Order',
      motto: 'Breathe and become one with the blade!'
    },
    'My Hero Academia': {
      name: 'My Hero Academia',
      icon: '💥',
      color: 'red',
      ranks: ['Student', 'Hero', 'Pro Hero', 'Symbol of Peace'],
      description: 'Plus Ultra! Become the greatest hero!',
      members: 1876,
      level: 'Hero Academy',
      motto: 'Go beyond! Plus Ultra!'
    }
  };

  const currentClan = clans[user.clan];
  const switchCost = 1000; // Points required to switch clans

  // Mock clan activities
  const activities = [
    { 
      id: 1, 
      type: 'challenge', 
      title: 'Weekly Anime Marathon', 
      description: 'Watch 20 episodes this week',
      reward: '100 points',
      participants: 234,
      timeLeft: '3 days',
      status: 'active'
    },
    { 
      id: 2, 
      type: 'tournament', 
      title: 'Clan Trivia Battle', 
      description: 'Test your anime knowledge',
      reward: '500 points + Rank boost',
      participants: 89,
      timeLeft: '1 day',
      status: 'active'
    },
    { 
      id: 3, 
      type: 'event', 
      title: 'Naruto Movie Night', 
      description: 'Community watch party',
      reward: 'Social points',
      participants: 156,
      timeLeft: 'Tonight 8PM',
      status: 'upcoming'
    }
  ];

  // Mock clan members (top contributors)
  const topMembers = [
    { username: 'NarutoHokage', rank: 'Kage', points: 5670, avatar: '👑' },
    { username: 'ShadowClone_Master', rank: 'Jonin', points: 4230, avatar: '🥷' },
    { username: 'RamenLover_99', rank: 'Jonin', points: 3890, avatar: '🍜' },
    { username: 'AnimeOtaku_2024', rank: 'Jonin', points: 2450, avatar: '🎭' },
    { username: 'LeafVillage_Ninja', rank: 'Chunin', points: 1980, avatar: '🍃' }
  ];

  const handleSwitchClan = (newClan) => {
    if (user.points >= switchCost) {
      setUser({
        ...user,
        clan: newClan,
        points: user.points - switchCost,
        rank: clans[newClan].ranks[0], // Reset to lowest rank
        clanJoinDate: new Date().toISOString().split('T')[0]
      });
      setShowSwitchModal(false);
    }
  };

  const getColorClasses = (color) => {
    const colors = {
      orange: { bg: 'bg-orange-500', text: 'text-orange-500', border: 'border-orange-500' },
      blue: { bg: 'bg-blue-500', text: 'text-blue-500', border: 'border-blue-500' },
      green: { bg: 'bg-green-500', text: 'text-green-500', border: 'border-green-500' },
      purple: { bg: 'bg-purple-500', text: 'text-purple-500', border: 'border-purple-500' },
      red: { bg: 'bg-red-500', text: 'text-red-500', border: 'border-red-500' }
    };
    return colors[color] || colors.orange;
  };

  const clanColors = getColorClasses(currentClan.color);

  return (
    <div className={`min-h-screen overflow-hidden transition-all duration-300 ${
      isNightMode
        ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900'
        : 'bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50'
    }`}>
      <div className="h-screen overflow-y-auto hide-scrollbar">
      
      {/* Floating Sakura Petals */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
        {[...Array(4)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-pink-300 rounded-full opacity-20"
            style={{
              left: `${Math.random() * 100}%`,
              top: `-10px`
            }}
            animate={{
              y: window.innerHeight + 50,
              x: [0, 30, -30, 0],
              rotate: 360,
              opacity: [0.2, 0.05, 0.2]
            }}
            transition={{
              duration: Math.random() * 3 + 12,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "linear"
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-6xl mx-auto p-4">
        
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={() => navigate('/home')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              isNightMode ? 'text-gray-300 hover:text-white hover:bg-gray-700' : 'text-gray-600 hover:text-gray-800 hover:bg-white'
            }`}
          >
            <FaArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </button>
          
          <h1 className={`text-2xl font-bold flex items-center space-x-2 ${
            isNightMode ? 'text-white' : 'text-gray-800'
          }`}>
            <span>{currentClan.icon}</span>
            <span>{currentClan.name} Clan</span>
          </h1>

          <button
            onClick={() => setShowSwitchModal(true)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              isNightMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            <FaExchangeAlt className="w-4 h-4" />
            <span>Switch Clan</span>
          </button>
        </div>

        {/* Clan Header Card */}
        <motion.div
          className={`rounded-2xl p-6 mb-6 backdrop-blur-sm ${
            isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
          }`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col md:flex-row items-center md:items-start space-y-4 md:space-y-0 md:space-x-6">
            
            {/* Clan Icon */}
            <div className={`w-20 h-20 rounded-full flex items-center justify-center text-4xl ${clanColors.bg} shadow-lg`}>
              {currentClan.icon}
            </div>

            {/* Clan Info */}
            <div className="flex-1 text-center md:text-left">
              <h2 className={`text-3xl font-bold mb-2 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                {currentClan.name} Clan
              </h2>
              <p className={`text-lg mb-2 ${clanColors.text} font-semibold`}>
                {currentClan.level}
              </p>
              <p className={`text-sm mb-4 ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {currentClan.description}
              </p>
              <div className={`text-sm italic ${clanColors.text}`}>
                "{currentClan.motto}"
              </div>
            </div>

            {/* User Stats */}
            <div className="text-center">
              <div className={`text-2xl font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                {user.rank}
              </div>
              <div className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                Your Rank
              </div>
              <div className={`text-lg font-semibold mt-2 ${clanColors.text}`}>
                {user.points} Points
              </div>
              <div className={`text-xs ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                Member since {new Date(user.clanJoinDate).toLocaleDateString()}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Navigation Tabs */}
        <div className={`flex space-x-1 mb-6 p-1 rounded-xl ${
          isNightMode ? 'bg-gray-800/50' : 'bg-white/50'
        }`}>
          {[
            { id: 'overview', label: 'Overview', icon: FaHome },
            { id: 'activities', label: 'Activities', icon: FaGamepad },
            { id: 'members', label: 'Members', icon: FaUsers },
            { id: 'chat', label: 'Clan Chat', icon: FaComments }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all ${
                activeTab === tab.id
                  ? `${clanColors.bg} text-white shadow-lg`
                  : isNightMode 
                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700/50' 
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span className="font-medium">{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          {activeTab === 'overview' && (
            <motion.div
              key="overview"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              {/* Clan Stats */}
              <div className={`p-6 rounded-xl backdrop-blur-sm ${
                isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
              }`}>
                <h3 className={`text-lg font-bold mb-4 flex items-center space-x-2 ${
                  isNightMode ? 'text-white' : 'text-gray-800'
                }`}>
                  <FaTrophy className={clanColors.text} />
                  <span>Clan Stats</span>
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className={isNightMode ? 'text-gray-300' : 'text-gray-600'}>Total Members</span>
                    <span className={`font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                      {currentClan.members.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className={isNightMode ? 'text-gray-300' : 'text-gray-600'}>Clan Level</span>
                    <span className={`font-bold ${clanColors.text}`}>
                      {currentClan.level}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className={isNightMode ? 'text-gray-300' : 'text-gray-600'}>Your Contributions</span>
                    <span className={`font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                      {user.clanContributions}
                    </span>
                  </div>
                </div>
              </div>

              {/* Recent Activities */}
              <div className={`p-6 rounded-xl backdrop-blur-sm ${
                isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
              }`}>
                <h3 className={`text-lg font-bold mb-4 flex items-center space-x-2 ${
                  isNightMode ? 'text-white' : 'text-gray-800'
                }`}>
                  <FaFire className={clanColors.text} />
                  <span>Recent Activity</span>
                </h3>
                <div className="space-y-3">
                  {activities.slice(0, 3).map((activity) => (
                    <div key={activity.id} className={`p-3 rounded-lg ${
                      isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                    }`}>
                      <div className={`font-medium text-sm ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                        {activity.title}
                      </div>
                      <div className={`text-xs ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {activity.participants} participants • {activity.timeLeft}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Top Members */}
              <div className={`p-6 rounded-xl backdrop-blur-sm ${
                isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
              }`}>
                <h3 className={`text-lg font-bold mb-4 flex items-center space-x-2 ${
                  isNightMode ? 'text-white' : 'text-gray-800'
                }`}>
                  <FaCrown className={clanColors.text} />
                  <span>Top Members</span>
                </h3>
                <div className="space-y-3">
                  {topMembers.slice(0, 3).map((member, index) => (
                    <div key={member.username} className="flex items-center space-x-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${
                        index === 0 ? 'bg-yellow-500' : 
                        index === 1 ? 'bg-gray-400' : 'bg-orange-600'
                      } text-white font-bold`}>
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <div className={`font-medium text-sm ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                          {member.username}
                        </div>
                        <div className={`text-xs ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          {member.rank} • {member.points} pts
                        </div>
                      </div>
                      <div className="text-lg">{member.avatar}</div>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'activities' && (
            <motion.div
              key="activities"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {activities.map((activity) => (
                  <div
                    key={activity.id}
                    className={`p-6 rounded-xl backdrop-blur-sm border-2 ${
                      activity.status === 'active'
                        ? `${clanColors.border} ${isNightMode ? 'bg-gray-800/90' : 'bg-white/90'}`
                        : isNightMode ? 'bg-gray-800/90 border-gray-600' : 'bg-white/90 border-gray-200'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <h3 className={`text-lg font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                        {activity.title}
                      </h3>
                      <span className={`px-3 py-1 rounded-full text-xs font-bold ${
                        activity.status === 'active'
                          ? `${clanColors.bg} text-white`
                          : isNightMode ? 'bg-gray-600 text-gray-300' : 'bg-gray-200 text-gray-600'
                      }`}>
                        {activity.status.toUpperCase()}
                      </span>
                    </div>
                    <p className={`text-sm mb-4 ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      {activity.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        <FaUsers className="inline w-3 h-3 mr-1" />
                        {activity.participants} participants
                      </div>
                      <div className={`text-sm font-semibold ${clanColors.text}`}>
                        {activity.reward}
                      </div>
                    </div>
                    <div className={`text-xs mt-2 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      ⏰ {activity.timeLeft}
                    </div>
                    <button className={`w-full mt-4 py-2 rounded-lg font-semibold transition-colors ${
                      activity.status === 'active'
                        ? `${clanColors.bg} text-white hover:opacity-90`
                        : isNightMode ? 'bg-gray-600 text-gray-300' : 'bg-gray-200 text-gray-600'
                    }`}>
                      {activity.status === 'active' ? 'Join Activity' : 'Coming Soon'}
                    </button>
                  </div>
                ))}
              </div>
            </motion.div>
          )}

          {activeTab === 'members' && (
            <motion.div
              key="members"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <div className={`p-6 rounded-xl backdrop-blur-sm ${
                isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
              }`}>
                <h3 className={`text-xl font-bold mb-6 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                  Top Clan Members
                </h3>
                <div className="space-y-4">
                  {topMembers.map((member, index) => (
                    <div
                      key={member.username}
                      className={`flex items-center space-x-4 p-4 rounded-lg ${
                        member.username === user.username
                          ? `${clanColors.bg}/20 border-2 ${clanColors.border}`
                          : isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                      }`}
                    >
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold ${
                        index === 0 ? 'bg-yellow-500 text-white' :
                        index === 1 ? 'bg-gray-400 text-white' :
                        index === 2 ? 'bg-orange-600 text-white' :
                        isNightMode ? 'bg-gray-600 text-gray-300' : 'bg-gray-200 text-gray-600'
                      }`}>
                        {index < 3 ? (index + 1) : member.avatar}
                      </div>
                      <div className="flex-1">
                        <div className={`font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                          {member.username}
                          {member.username === user.username && (
                            <span className={`ml-2 text-sm ${clanColors.text}`}>(You)</span>
                          )}
                        </div>
                        <div className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          {member.rank} • {member.points.toLocaleString()} points
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`text-lg font-bold ${clanColors.text}`}>
                          #{index + 1}
                        </div>
                        {index < 3 && (
                          <div className="text-xs text-yellow-500">
                            {index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉'}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'chat' && (
            <motion.div
              key="chat"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <div className={`p-6 rounded-xl backdrop-blur-sm ${
                isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
              }`}>
                <h3 className={`text-xl font-bold mb-6 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                  Clan Chat - Coming Soon! 💬
                </h3>
                <div className="text-center py-12">
                  <FaComments className={`w-16 h-16 mx-auto mb-4 ${clanColors.text}`} />
                  <p className={`text-lg mb-2 ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Clan chat feature is under development
                  </p>
                  <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    Soon you'll be able to chat with your clan members in real-time!
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Clan Switch Modal */}
        <AnimatePresence>
          {showSwitchModal && (
            <motion.div
              className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <motion.div
                className={`rounded-2xl p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto hide-scrollbar ${
                  isNightMode ? 'bg-gray-800' : 'bg-white'
                }`}
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
              >
                <div className="flex items-center justify-between mb-6">
                  <h3 className={`text-xl font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                    Switch Clan
                  </h3>
                  <button
                    onClick={() => setShowSwitchModal(false)}
                    className={`p-2 rounded-lg transition-colors ${
                      isNightMode ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    ✕
                  </button>
                </div>

                {/* Warning */}
                <div className={`p-4 rounded-lg mb-6 border-l-4 border-yellow-500 ${
                  isNightMode ? 'bg-yellow-500/10' : 'bg-yellow-50'
                }`}>
                  <div className="flex items-start space-x-3">
                    <FaExclamationTriangle className="text-yellow-500 w-5 h-5 mt-0.5" />
                    <div>
                      <h4 className={`font-bold ${isNightMode ? 'text-yellow-400' : 'text-yellow-700'}`}>
                        Warning: Clan Switch Cost
                      </h4>
                      <p className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        Switching clans costs <strong>{switchCost} points</strong> and resets your rank to the lowest level.
                        You currently have <strong>{user.points} points</strong>.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Available Clans */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(clans).map(([clanKey, clan]) => {
                    const isCurrentClan = clanKey === user.clan;
                    const canAfford = user.points >= switchCost;
                    const colors = getColorClasses(clan.color);

                    return (
                      <div
                        key={clanKey}
                        className={`p-4 rounded-lg border-2 transition-all ${
                          isCurrentClan
                            ? `${colors.border} ${colors.bg}/20`
                            : isNightMode ? 'border-gray-600 hover:border-gray-500' : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-center space-x-3 mb-3">
                          <div className={`w-12 h-12 rounded-full flex items-center justify-center text-2xl ${colors.bg}`}>
                            {clan.icon}
                          </div>
                          <div>
                            <h4 className={`font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                              {clan.name}
                            </h4>
                            <p className={`text-sm ${colors.text}`}>
                              {clan.level}
                            </p>
                          </div>
                        </div>
                        <p className={`text-sm mb-3 ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                          {clan.description}
                        </p>
                        <div className={`text-xs mb-4 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          {clan.members.toLocaleString()} members
                        </div>

                        {isCurrentClan ? (
                          <div className={`text-center py-2 rounded-lg ${colors.bg} text-white font-semibold`}>
                            Current Clan
                          </div>
                        ) : (
                          <button
                            onClick={() => handleSwitchClan(clanKey)}
                            disabled={!canAfford}
                            className={`w-full py-2 rounded-lg font-semibold transition-colors ${
                              canAfford
                                ? `${colors.bg} text-white hover:opacity-90`
                                : isNightMode ? 'bg-gray-600 text-gray-400 cursor-not-allowed' : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                            }`}
                          >
                            {canAfford ? `Switch (${switchCost} pts)` : 'Insufficient Points'}
                          </button>
                        )}
                      </div>
                    );
                  })}
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

export default Clan;
