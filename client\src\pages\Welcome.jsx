import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useAuthStore } from '../store/authStore'
import { Sparkles, Heart, Star } from 'lucide-react'

const Welcome = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()

  useEffect(() => {
    // Auto-redirect to home after 5 seconds
    const timer = setTimeout(() => {
      navigate('/home')
    }, 5000)

    return () => clearTimeout(timer)
  }, [navigate])

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  }

  const iconVariants = {
    hidden: { scale: 0, rotate: -180 },
    visible: {
      scale: 1,
      rotate: 0,
      transition: {
        duration: 1,
        ease: "easeOut",
        delay: 0.5
      }
    },
    float: {
      y: [-10, 10, -10],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  }

  const textVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.8,
        delay: 1
      }
    }
  }

  const sparkleVariants = {
    hidden: { opacity: 0, scale: 0 },
    visible: {
      opacity: [0, 1, 0],
      scale: [0, 1, 0],
      transition: {
        duration: 2,
        repeat: Infinity,
        delay: 1.5
      }
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-secondary-50 to-accent-50 dark:from-gray-900 dark:via-purple-900 dark:to-blue-900 flex items-center justify-center overflow-hidden relative">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Floating Sakura Petals */}
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-3 h-3 bg-pink-300 rounded-full opacity-60"
            initial={{ 
              x: Math.random() * window.innerWidth, 
              y: -50,
              rotate: 0 
            }}
            animate={{ 
              y: window.innerHeight + 50,
              rotate: 360,
              x: Math.random() * window.innerWidth
            }}
            transition={{
              duration: Math.random() * 3 + 5,
              repeat: Infinity,
              delay: Math.random() * 2,
              ease: "linear"
            }}
          />
        ))}

        {/* Sparkle Effects */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={`sparkle-${i}`}
            className="absolute"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`
            }}
            variants={sparkleVariants}
            initial="hidden"
            animate="visible"
          >
            <Sparkles className="text-yellow-400 w-6 h-6" />
          </motion.div>
        ))}
      </div>

      {/* Main Content */}
      <motion.div
        className="text-center z-10 px-4"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Animated Logo/Icon */}
        <motion.div
          className="mb-8 flex justify-center"
          variants={iconVariants}
          initial="hidden"
          animate={["visible", "float"]}
        >
          <div className="relative">
            <div className="w-32 h-32 anime-gradient rounded-full flex items-center justify-center shadow-2xl">
              <span className="text-white font-bold text-6xl">A</span>
            </div>
            
            {/* Orbiting Hearts */}
            <motion.div
              className="absolute -top-2 -right-2"
              animate={{ rotate: 360 }}
              transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
            >
              <Heart className="text-red-500 w-8 h-8 fill-current" />
            </motion.div>
            
            <motion.div
              className="absolute -bottom-2 -left-2"
              animate={{ rotate: -360 }}
              transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
            >
              <Star className="text-yellow-500 w-6 h-6 fill-current" />
            </motion.div>
          </div>
        </motion.div>

        {/* Welcome Text */}
        <motion.div variants={itemVariants}>
          <motion.h1
            className="text-6xl md:text-8xl font-bold mb-4"
            variants={textVariants}
          >
            <span className="text-gradient">AnimeVerse</span>
          </motion.h1>
        </motion.div>

        <motion.div variants={itemVariants}>
          <motion.p
            className="text-2xl md:text-3xl text-gray-700 dark:text-gray-300 mb-6"
            variants={textVariants}
          >
            Welcome{user?.username ? `, ${user.username}` : ''}! 🌸
          </motion.p>
        </motion.div>

        <motion.div variants={itemVariants}>
          <motion.p
            className="text-lg md:text-xl text-gray-600 dark:text-gray-400 mb-8"
            variants={textVariants}
          >
            Your anime journey begins now...
          </motion.p>
        </motion.div>

        {/* Loading Animation */}
        <motion.div
          className="flex justify-center items-center space-x-2"
          variants={itemVariants}
        >
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              className="w-4 h-4 bg-primary-500 rounded-full"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.2
              }}
            />
          ))}
        </motion.div>

        {/* Progress Bar */}
        <motion.div
          className="mt-8 w-64 mx-auto"
          variants={itemVariants}
        >
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <motion.div
              className="anime-gradient h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: "100%" }}
              transition={{ duration: 5, ease: "easeInOut" }}
            />
          </div>
          <motion.p
            className="text-sm text-gray-500 dark:text-gray-400 mt-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2 }}
          >
            Preparing your anime experience...
          </motion.p>
        </motion.div>

        {/* Skip Button */}
        <motion.button
          className="mt-8 text-primary-600 hover:text-primary-700 underline text-sm"
          onClick={() => navigate('/home')}
          variants={itemVariants}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          Skip intro
        </motion.button>
      </motion.div>

      {/* Glow Effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-primary-500/10 via-secondary-500/10 to-accent-500/10 pointer-events-none"
        animate={{
          opacity: [0.3, 0.6, 0.3]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  )
}

export default Welcome
