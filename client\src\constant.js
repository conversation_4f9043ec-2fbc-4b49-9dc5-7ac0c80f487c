// API Base URL Configuration
export const BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://your-production-api.com/api' 
  : 'http://localhost:5000/api';

// Other constants
export const APP_NAME = 'AnimeVerse';
export const APP_VERSION = '1.0.0';

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  LOGOUT: '/auth/logout',
  REFRESH_TOKEN: '/auth/refresh',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',
  
  // Users
  PROFILE: '/users/profile',
  UPDATE_PROFILE: '/users/profile',
  USERS: '/users',
  
  // Posts
  POSTS: '/posts',
  CREATE_POST: '/posts',
  LIKE_POST: '/posts/:id/like',
  COMMENT_POST: '/posts/:id/comments',
  
  // Communities
  COMMUNITIES: '/communities',
  JOIN_COMMUNITY: '/communities/:id/join',
  LEAVE_COMMUNITY: '/communities/:id/leave',
  
  // Events
  EVENTS: '/events',
  RSVP_EVENT: '/events/:id/rsvp',
  
  // Marketplace
  PRODUCTS: '/marketplace/products',
  ORDERS: '/marketplace/orders',
  
  // Health Check
  HEALTH: '/health'
};

// Local Storage Keys
export const STORAGE_KEYS = {
  TOKEN: 'animeverse_token',
  USER: 'animeverse_user',
  THEME: 'animeverse_theme',
  LANGUAGE: 'animeverse_language'
};

// Supported Languages
export const LANGUAGES = {
  EN: 'en',
  FR: 'fr'
};

// Anime Communities
export const ANIME_COMMUNITIES = {
  ONE_PIECE: 'one-piece',
  NARUTO: 'naruto',
  ATTACK_TITAN: 'attack-titan',
  DEMON_SLAYER: 'demon-slayer',
  MY_HERO_ACADEMIA: 'my-hero-academia',
  DRAGON_BALL: 'dragon-ball'
};

// File Upload Limits
export const UPLOAD_LIMITS = {
  IMAGE_MAX_SIZE: 5 * 1024 * 1024, // 5MB
  VIDEO_MAX_SIZE: 50 * 1024 * 1024, // 50MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_VIDEO_TYPES: ['video/mp4', 'video/webm', 'video/ogg']
};
