import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FaArrowLeft, FaBell, FaCheck, FaTrash, FaFilter, FaSearch,
  FaHeart, FaComment, FaUserPlus, FaCalendarAlt, FaTag,
  FaPlay, FaFire, FaCoins, FaExclamationTriangle, FaCog
} from 'react-icons/fa';
import { useTheme } from '../context/ThemeContext';
import { useNavigate } from 'react-router-dom';

const SpiritBell = () => {
  const { isNightMode } = useTheme();
  const navigate = useNavigate();
  
  const [activeTab, setActiveTab] = useState('all'); // all, social, episodes, events, marketplace
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedNotifications, setSelectedNotifications] = useState([]);

  // Filter states
  const [filters, setFilters] = useState({
    type: 'all', // all, social, episode, event, marketplace, system
    status: 'all', // all, read, unread
    timeRange: 'all' // all, today, week, month
  });

  // Mock notifications data
  const notifications = [
    {
      id: 1,
      type: 'episode',
      title: 'New Episode Alert',
      message: 'One Piece Episode 1100 is now available!',
      timestamp: '2024-01-15T16:30:00Z',
      isRead: false,
      icon: '📺',
      color: 'blue',
      actionUrl: '/anime-realm',
      metadata: {
        animeTitle: 'One Piece',
        episode: 1100,
        clan: 'One Piece'
      }
    },
    {
      id: 2,
      type: 'social',
      title: 'New Like',
      message: 'AnimeKing_2024 liked your post about Demon Slayer',
      timestamp: '2024-01-15T15:45:00Z',
      isRead: false,
      icon: '❤️',
      color: 'red',
      actionUrl: '/home',
      metadata: {
        user: 'AnimeKing_2024',
        postId: 123,
        clan: 'Demon Slayer'
      }
    },
    {
      id: 3,
      type: 'social',
      title: 'New Comment',
      message: 'MangaCollector_Pro commented on your review: "Great analysis!"',
      timestamp: '2024-01-15T14:20:00Z',
      isRead: true,
      icon: '💬',
      color: 'green',
      actionUrl: '/home',
      metadata: {
        user: 'MangaCollector_Pro',
        postId: 122,
        comment: 'Great analysis!'
      }
    },
    {
      id: 4,
      type: 'event',
      title: 'Event Reminder',
      message: 'Anime Expo 2024 starts in 2 days! Don\'t forget to prepare.',
      timestamp: '2024-01-15T12:00:00Z',
      isRead: false,
      icon: '🎪',
      color: 'purple',
      actionUrl: '/festivals',
      metadata: {
        eventId: 1,
        eventTitle: 'Anime Expo 2024',
        daysUntil: 2
      }
    },
    {
      id: 5,
      type: 'marketplace',
      title: 'Price Drop Alert',
      message: 'Demon Slayer Tanjiro Figure dropped to $89.99 (was $120.00)',
      timestamp: '2024-01-15T11:30:00Z',
      isRead: true,
      icon: '🏷️',
      color: 'orange',
      actionUrl: '/eden-market',
      metadata: {
        itemId: 1,
        itemTitle: 'Demon Slayer Tanjiro Figure',
        oldPrice: 120.00,
        newPrice: 89.99,
        discount: 25
      }
    },
    {
      id: 6,
      type: 'social',
      title: 'New Follower',
      message: 'NarutoFan_Ultimate started following you',
      timestamp: '2024-01-15T10:15:00Z',
      isRead: true,
      icon: '👤',
      color: 'blue',
      actionUrl: '/profile',
      metadata: {
        user: 'NarutoFan_Ultimate',
        clan: 'Naruto'
      }
    },
    {
      id: 7,
      type: 'event',
      title: 'Watch Party Starting',
      message: 'One Piece Episode 1100 Watch Party starts in 15 minutes!',
      timestamp: '2024-01-15T09:45:00Z',
      isRead: false,
      icon: '▶️',
      color: 'red',
      actionUrl: '/festivals',
      metadata: {
        eventId: 2,
        eventTitle: 'One Piece Episode 1100 Watch Party',
        minutesUntil: 15
      }
    },
    {
      id: 8,
      type: 'system',
      title: 'Clan Rank Up',
      message: 'Congratulations! You\'ve been promoted to Jonin in the Naruto clan!',
      timestamp: '2024-01-15T08:30:00Z',
      isRead: true,
      icon: '🏆',
      color: 'yellow',
      actionUrl: '/clan',
      metadata: {
        clan: 'Naruto',
        oldRank: 'Chunin',
        newRank: 'Jonin'
      }
    }
  ];

  const notificationTypes = [
    { id: 'all', name: 'All', icon: FaBell, color: 'purple' },
    { id: 'social', name: 'Social', icon: FaHeart, color: 'red' },
    { id: 'episode', name: 'Episodes', icon: FaPlay, color: 'blue' },
    { id: 'event', name: 'Events', icon: FaCalendarAlt, color: 'green' },
    { id: 'marketplace', name: 'Market', icon: FaTag, color: 'orange' },
    { id: 'system', name: 'System', icon: FaCog, color: 'gray' }
  ];

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'social': return FaHeart;
      case 'episode': return FaPlay;
      case 'event': return FaCalendarAlt;
      case 'marketplace': return FaTag;
      case 'system': return FaCog;
      default: return FaBell;
    }
  };

  const getNotificationColor = (color) => {
    switch (color) {
      case 'red': return 'text-red-500 bg-red-500/20';
      case 'blue': return 'text-blue-500 bg-blue-500/20';
      case 'green': return 'text-green-500 bg-green-500/20';
      case 'purple': return 'text-purple-500 bg-purple-500/20';
      case 'orange': return 'text-orange-500 bg-orange-500/20';
      case 'yellow': return 'text-yellow-500 bg-yellow-500/20';
      default: return 'text-gray-500 bg-gray-500/20';
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    if (searchQuery && !notification.title.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !notification.message.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    if (activeTab !== 'all' && notification.type !== activeTab) {
      return false;
    }
    if (filters.status !== 'all') {
      if (filters.status === 'read' && !notification.isRead) return false;
      if (filters.status === 'unread' && notification.isRead) return false;
    }
    return true;
  });

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const formatTime = (timestamp) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now - time) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const handleMarkAsRead = (notificationId) => {
    console.log('Marking as read:', notificationId);
    // Handle mark as read
  };

  const handleMarkAllAsRead = () => {
    console.log('Marking all as read');
    // Handle mark all as read
  };

  const handleDeleteNotification = (notificationId) => {
    console.log('Deleting notification:', notificationId);
    // Handle delete notification
  };

  const handleNotificationClick = (notification) => {
    if (!notification.isRead) {
      handleMarkAsRead(notification.id);
    }
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }
  };

  return (
    // 🔧 SCROLLBAR PATTERN - Consistent with all pages
    <div className={`min-h-screen overflow-hidden transition-all duration-300 ${
      isNightMode 
        ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900' 
        : 'bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50'
    }`}>
      <div className="h-screen overflow-y-auto hide-scrollbar">
        
        {/* 🌸 Floating Sakura Petals */}
        <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-pink-300 rounded-full opacity-20"
              style={{
                left: `${Math.random() * 100}%`,
                top: `-10px`
              }}
              animate={{
                y: window.innerHeight + 50,
                x: [0, 30, -30, 0],
                rotate: 360,
                opacity: [0.2, 0.05, 0.2]
              }}
              transition={{
                duration: Math.random() * 3 + 12,
                repeat: Infinity,
                delay: Math.random() * 5,
                ease: "linear"
              }}
            />
          ))}
        </div>

        <div className="relative z-10 max-w-4xl mx-auto p-4">
          
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={() => navigate('/home')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                isNightMode ? 'text-gray-300 hover:text-white hover:bg-gray-700' : 'text-gray-600 hover:text-gray-800 hover:bg-white'
              }`}
            >
              <FaArrowLeft className="w-4 h-4" />
              <span>Back to Home</span>
            </button>
            
            <h1 className={`text-3xl font-bold flex items-center space-x-3 ${
              isNightMode ? 'text-white' : 'text-gray-800'
            }`}>
              <span>🔔</span>
              <span>Spirit Bell</span>
              {unreadCount > 0 && (
                <span className="bg-red-500 text-white text-sm px-3 py-1 rounded-full">
                  {unreadCount}
                </span>
              )}
            </h1>

            <div className="flex items-center space-x-2">
              <button
                onClick={handleMarkAllAsRead}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  isNightMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <FaCheck className="w-4 h-4" />
                <span>Mark All Read</span>
              </button>
            </div>
          </div>

          {/* Search and Filters */}
          <div className={`p-6 rounded-2xl mb-6 backdrop-blur-sm ${
            isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
          }`}>
            <div className="flex flex-col md:flex-row gap-4">
              
              {/* Search Bar */}
              <div className="flex-1 relative">
                <FaSearch className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${
                  isNightMode ? 'text-gray-400' : 'text-gray-500'
                }`} />
                <input
                  type="text"
                  placeholder="Search notifications..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-colors ${
                    isNightMode
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500'
                      : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500'
                  } focus:outline-none focus:ring-0`}
                />
              </div>

              {/* Filter Button */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center space-x-2 px-6 py-3 rounded-lg transition-colors ${
                  showFilters
                    ? 'bg-purple-500 text-white'
                    : isNightMode 
                      ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <FaFilter className="w-4 h-4" />
                <span>Filters</span>
              </button>
            </div>

            {/* Advanced Filters */}
            <AnimatePresence>
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="mt-6 pt-6 border-t border-gray-300"
                >
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    
                    {/* Status Filter */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${
                        isNightMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Status
                      </label>
                      <select
                        value={filters.status}
                        onChange={(e) => setFilters({...filters, status: e.target.value})}
                        className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                          isNightMode
                            ? 'bg-gray-700 border-gray-600 text-white'
                            : 'bg-gray-50 border-gray-300 text-gray-900'
                        } focus:outline-none focus:ring-0 focus:border-purple-500`}
                      >
                        <option value="all">All Notifications</option>
                        <option value="unread">Unread Only</option>
                        <option value="read">Read Only</option>
                      </select>
                    </div>

                    {/* Type Filter */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${
                        isNightMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Type
                      </label>
                      <select
                        value={filters.type}
                        onChange={(e) => setFilters({...filters, type: e.target.value})}
                        className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                          isNightMode
                            ? 'bg-gray-700 border-gray-600 text-white'
                            : 'bg-gray-50 border-gray-300 text-gray-900'
                        } focus:outline-none focus:ring-0 focus:border-purple-500`}
                      >
                        {notificationTypes.map(type => (
                          <option key={type.id} value={type.id}>
                            {type.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Time Range Filter */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${
                        isNightMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Time Range
                      </label>
                      <select
                        value={filters.timeRange}
                        onChange={(e) => setFilters({...filters, timeRange: e.target.value})}
                        className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                          isNightMode
                            ? 'bg-gray-700 border-gray-600 text-white'
                            : 'bg-gray-50 border-gray-300 text-gray-900'
                        } focus:outline-none focus:ring-0 focus:border-purple-500`}
                      >
                        <option value="all">All Time</option>
                        <option value="today">Today</option>
                        <option value="week">This Week</option>
                        <option value="month">This Month</option>
                      </select>
                    </div>
                  </div>

                  {/* Clear Filters */}
                  <div className="mt-4 flex justify-end">
                    <button
                      onClick={() => setFilters({
                        type: 'all',
                        status: 'all',
                        timeRange: 'all'
                      })}
                      className={`px-4 py-2 rounded-lg text-sm transition-colors ${
                        isNightMode 
                          ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700' 
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      Clear All Filters
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Notification Type Categories */}
          <div className={`p-4 rounded-2xl mb-6 backdrop-blur-sm ${
            isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
          }`}>
            <div className="flex space-x-2 overflow-x-auto hide-scrollbar pb-2">
              {notificationTypes.map((type) => (
                <button
                  key={type.id}
                  onClick={() => setActiveTab(type.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-all ${
                    activeTab === type.id
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                      : isNightMode 
                        ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <type.icon className="w-4 h-4" />
                  <span className="font-medium">{type.name}</span>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    activeTab === type.id
                      ? 'bg-white/20 text-white'
                      : isNightMode ? 'bg-gray-600 text-gray-300' : 'bg-gray-200 text-gray-600'
                  }`}>
                    {type.id === 'all' 
                      ? notifications.length 
                      : notifications.filter(n => n.type === type.id).length
                    }
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Notifications List */}
          <div className="space-y-4">
            {filteredNotifications.length > 0 ? (
              filteredNotifications.map((notification, index) => (
                <motion.div
                  key={notification.id}
                  className={`p-6 rounded-xl backdrop-blur-sm transition-all cursor-pointer ${
                    notification.isRead
                      ? isNightMode ? 'bg-gray-800/60 hover:bg-gray-800/80' : 'bg-white/60 hover:bg-white/80'
                      : isNightMode ? 'bg-gray-800/90 hover:bg-gray-700/90 border-l-4 border-purple-500' : 'bg-white/90 hover:bg-gray-50/90 border-l-4 border-purple-500'
                  }`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start space-x-4">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                      getNotificationColor(notification.color)
                    }`}>
                      <span className="text-2xl">{notification.icon}</span>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className={`font-semibold ${
                          isNightMode ? 'text-white' : 'text-gray-800'
                        }`}>
                          {notification.title}
                        </h3>
                        <div className="flex items-center space-x-2">
                          <span className={`text-sm ${
                            isNightMode ? 'text-gray-400' : 'text-gray-500'
                          }`}>
                            {formatTime(notification.timestamp)}
                          </span>
                          {!notification.isRead && (
                            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                          )}
                        </div>
                      </div>
                      
                      <p className={`text-sm mb-3 ${
                        isNightMode ? 'text-gray-300' : 'text-gray-600'
                      }`}>
                        {notification.message}
                      </p>
                      
                      {/* Notification Actions */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {notification.metadata && (
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              isNightMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'
                            }`}>
                              {notification.metadata.clan || notification.metadata.animeTitle || 'System'}
                            </span>
                          )}
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          {!notification.isRead && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleMarkAsRead(notification.id);
                              }}
                              className={`p-2 rounded-lg transition-colors ${
                                isNightMode ? 'text-gray-400 hover:text-green-500 hover:bg-gray-700' : 'text-gray-500 hover:text-green-600 hover:bg-gray-100'
                              }`}
                            >
                              <FaCheck className="w-4 h-4" />
                            </button>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteNotification(notification.id);
                            }}
                            className={`p-2 rounded-lg transition-colors ${
                              isNightMode ? 'text-gray-400 hover:text-red-500 hover:bg-gray-700' : 'text-gray-500 hover:text-red-600 hover:bg-gray-100'
                            }`}
                          >
                            <FaTrash className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))
            ) : (
              /* No Notifications */
              <div className={`text-center py-12 rounded-xl backdrop-blur-sm ${
                isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
              }`}>
                <FaBell className={`w-16 h-16 mx-auto mb-4 ${isNightMode ? 'text-gray-600' : 'text-gray-300'}`} />
                <p className={`text-lg mb-2 ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  No notifications found
                </p>
                <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  {searchQuery ? 'Try adjusting your search or filters' : 'You\'re all caught up!'}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SpiritBell;
