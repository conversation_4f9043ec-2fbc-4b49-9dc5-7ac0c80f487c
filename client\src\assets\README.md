# Assets Folder

This folder contains all the static assets for the AnimeVerse application.

## Image Files Needed

You need to add the following PNG image files to this folder:

### 1. hand_wave.png
- **Description**: A hand waving emoji or anime-style hand waving illustration
- **Recommended size**: 64x64px or 128x128px
- **Usage**: Welcome messages, greetings
- **Suggested sources**: 
  - Emoji PNG from https://emojipedia.org/
  - Anime-style hand wave illustration
  - Custom illustration

### 2. header_img.png
- **Description**: Header/hero image for the landing page
- **Recommended size**: 1200x600px or similar wide format
- **Usage**: Main header section, hero banner
- **Suggested content**: 
  - Anime characters collage
  - <PERSON> petals background
  - AnimeVerse themed illustration
  - Community/social gathering anime art

## How to Add Images

1. **Download or create** the images according to the specifications above
2. **Save them** in this folder (`client/src/assets/`)
3. **Name them exactly** as specified:
   - `hand_wave.png`
   - `header_img.png`

## Alternative Sources for Images

- **Free anime illustrations**: https://www.pixabay.com/ (search "anime")
- **Emoji PNGs**: https://emojipedia.org/
- **Free illustrations**: https://undraw.co/ (can be customized)
- **Anime stock photos**: https://unsplash.com/ (search "anime" or "manga")

## SVG Icons

The following SVG icons are already created and ready to use:
- ✅ arrow_icon.svg
- ✅ lock_icon.svg  
- ✅ logo.svg
- ✅ mail_icon.svg
- ✅ person_icon.svg

## Usage in Code

All assets are exported from the `assets/index.js` file and can be imported like this:

```javascript
import { assets } from './assets';

// Use in components
<img src={assets.hand_wave} alt="Hand wave" />
<img src={assets.header_img} alt="Header" />
```
