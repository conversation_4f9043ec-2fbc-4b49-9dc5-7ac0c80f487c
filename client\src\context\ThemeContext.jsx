import React, { createContext, useContext, useState, useEffect } from 'react';
import { STORAGE_KEYS } from '../constant';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [isNightMode, setIsNightMode] = useState(false);
  const [animeTheme, setAnimeTheme] = useState('default');

  // Load theme from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem(STORAGE_KEYS.THEME);
    if (savedTheme) {
      const themeData = JSON.parse(savedTheme);
      setIsNightMode(themeData.isNightMode || false);
      setAnimeTheme(themeData.animeTheme || 'default');
    }
  }, []);

  // Save theme to localStorage when it changes
  useEffect(() => {
    const themeData = {
      isNightMode,
      animeTheme
    };
    localStorage.setItem(STORAGE_KEYS.THEME, JSON.stringify(themeData));
    
    // Apply dark class to document
    if (isNightMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isNightMode, animeTheme]);

  const toggleNightMode = () => {
    setIsNightMode(prev => !prev);
  };

  const changeAnimeTheme = (theme) => {
    setAnimeTheme(theme);
  };

  const getThemeColors = () => {
    const themes = {
      default: {
        primary: 'from-anime-purple to-anime-pink',
        secondary: 'from-anime-blue to-anime-cyan',
        accent: 'anime-purple'
      },
      'one-piece': {
        primary: 'from-red-500 to-orange-500',
        secondary: 'from-orange-400 to-yellow-400',
        accent: 'red-500'
      },
      'naruto': {
        primary: 'from-orange-500 to-yellow-500',
        secondary: 'from-yellow-400 to-orange-400',
        accent: 'orange-500'
      },
      'demon-slayer': {
        primary: 'from-purple-600 to-pink-600',
        secondary: 'from-pink-400 to-purple-400',
        accent: 'purple-600'
      }
    };
    
    return themes[animeTheme] || themes.default;
  };

  const value = {
    isNightMode,
    animeTheme,
    toggleNightMode,
    changeAnimeTheme,
    getThemeColors
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
