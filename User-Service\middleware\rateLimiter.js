const { RateLimiterRedis } = require('rate-limiter-flexible');
const redis = require('redis');

const redisClient = redis.createClient({
  url: process.env.REDIS_URL
});

redisClient.on('error', (err) => console.error('Redis error:', err));

const loginRateLimiter = new RateLimiterRedis({
  storeClient: redisClient,
  keyPrefix: 'login_limit',
  points: 5, // 5 attempts
  duration: 60, // per 60 seconds
  blockDuration: 300 // block for 5 minutes after exceeding
});

const apiRateLimiter = new RateLimiterRedis({
  storeClient: redisClient,
  keyPrefix: 'api_limit',
  points: 100, // 100 requests
  duration: 60 // per minute
});

module.exports = { loginRateLimiter, apiRateLimiter };