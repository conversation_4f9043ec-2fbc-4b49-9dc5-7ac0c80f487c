@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@import "tailwindcss";

/* Custom Anime-themed styles */
.anime-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Primary color variants */
.bg-primary-50 { background-color: #f0f9ff; }
.bg-primary-500 { background-color: #3b82f6; }
.text-primary-500 { color: #3b82f6; }
.text-primary-600 { color: #2563eb; }
.text-primary-700 { color: #1d4ed8; }

/* Secondary color variants */
.bg-secondary-50 { background-color: #fdf4ff; }
.bg-secondary-500 { color: #a855f7; }

/* Accent color variants */
.bg-accent-50 { background-color: #fef7ff; }
.bg-accent-500 { color: #d946ef; }

/* Hide scrollbars */
.hide-scrollbar {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}