import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaArrowLeft, FaCog, Fa<PERSON>ser, FaBell, FaLock, FaDownload, FaTrash,
  FaUserShield, FaChartBar, FaEye, FaEyeSlash, FaToggleOn, FaToggleOff,
  FaGlobe, FaPalette, FaVolumeUp, FaDatabase, FaExclamationTriangle
} from 'react-icons/fa';
import { useTheme } from '../context/ThemeContext';
import { useNavigate } from 'react-router-dom';

const Settings = () => {
  const { isNightMode, toggleTheme } = useTheme();
  const navigate = useNavigate();
  
  const [activeTab, setActiveTab] = useState('profile'); // profile, privacy, notifications, account, admin, analytics
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isAdmin] = useState(true); // Mock admin status

  // User settings state
  const [userSettings, setUserSettings] = useState({
    // Profile Settings
    username: 'AnimeOtaku_2024',
    email: '<EMAIL>',
    bio: 'Passionate anime fan and manga collector',
    clan: 'Naruto',
    profileVisibility: 'public', // public, friends, private
    
    // Privacy Settings
    showOnlineStatus: true,
    allowDirectMessages: 'everyone', // everyone, friends, clan, none
    showWatchingList: true,
    showClanActivity: true,
    dataCollection: true,
    
    // Notification Settings
    emailNotifications: true,
    pushNotifications: true,
    episodeAlerts: true,
    socialNotifications: true,
    eventReminders: true,
    marketplaceAlerts: true,
    clanNotifications: true,
    
    // Appearance Settings
    theme: isNightMode ? 'dark' : 'light',
    language: 'en', // en, fr
    animationsEnabled: true,
    soundEffects: true,
    
    // Account Settings
    twoFactorAuth: false,
    loginAlerts: true,
    sessionTimeout: 30 // minutes
  });

  // Mock analytics data
  const analyticsData = {
    totalUsers: 15420,
    activeUsers: 8934,
    newUsersToday: 234,
    totalPosts: 45678,
    totalEvents: 156,
    totalMarketplaceItems: 2341,
    
    userGrowth: [
      { month: 'Jan', users: 12000 },
      { month: 'Feb', users: 13200 },
      { month: 'Mar', users: 14100 },
      { month: 'Apr', users: 15420 }
    ],
    
    clanDistribution: [
      { clan: 'Naruto', count: 4521, percentage: 29.3 },
      { clan: 'One Piece', count: 3876, percentage: 25.1 },
      { clan: 'Demon Slayer', count: 3234, percentage: 21.0 },
      { clan: 'Attack on Titan', count: 2456, percentage: 15.9 },
      { clan: 'Dragon Ball', count: 1333, percentage: 8.6 }
    ],
    
    engagementMetrics: {
      dailyActiveUsers: 5678,
      averageSessionTime: '24 minutes',
      postsPerDay: 1234,
      messagesPerDay: 8765,
      eventsPerWeek: 45
    }
  };

  const handleSettingChange = (category, setting, value) => {
    setUserSettings(prev => ({
      ...prev,
      [setting]: value
    }));
    console.log(`Updated ${category}.${setting} to:`, value);
  };

  const handleExportData = () => {
    console.log('Exporting user data...');
    // Handle data export
  };

  const handleDeleteAccount = () => {
    console.log('Deleting account...');
    setShowDeleteConfirm(false);
    // Handle account deletion
  };

  const settingsTabs = [
    { id: 'profile', label: 'Profile', icon: FaUser },
    { id: 'privacy', label: 'Privacy', icon: FaLock },
    { id: 'notifications', label: 'Notifications', icon: FaBell },
    { id: 'appearance', label: 'Appearance', icon: FaPalette },
    { id: 'account', label: 'Account', icon: FaCog },
    ...(isAdmin ? [
      { id: 'admin', label: 'Admin Panel', icon: FaUserShield },
      { id: 'analytics', label: 'Analytics', icon: FaChartBar }
    ] : [])
  ];

  return (
    // 🔧 SCROLLBAR PATTERN - Consistent with all pages
    <div className={`min-h-screen overflow-hidden transition-all duration-300 ${
      isNightMode 
        ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900' 
        : 'bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50'
    }`}>
      <div className="h-screen overflow-y-auto hide-scrollbar">
        
        {/* 🌸 Floating Sakura Petals */}
        <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-pink-300 rounded-full opacity-20"
              style={{
                left: `${Math.random() * 100}%`,
                top: `-10px`
              }}
              animate={{
                y: window.innerHeight + 50,
                x: [0, 30, -30, 0],
                rotate: 360,
                opacity: [0.2, 0.05, 0.2]
              }}
              transition={{
                duration: Math.random() * 3 + 12,
                repeat: Infinity,
                delay: Math.random() * 5,
                ease: "linear"
              }}
            />
          ))}
        </div>

        <div className="relative z-10 max-w-6xl mx-auto p-4">
          
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={() => navigate('/home')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                isNightMode ? 'text-gray-300 hover:text-white hover:bg-gray-700' : 'text-gray-600 hover:text-gray-800 hover:bg-white'
              }`}
            >
              <FaArrowLeft className="w-4 h-4" />
              <span>Back to Home</span>
            </button>
            
            <h1 className={`text-3xl font-bold flex items-center space-x-3 ${
              isNightMode ? 'text-white' : 'text-gray-800'
            }`}>
              <span>⚙️</span>
              <span>Settings</span>
            </h1>

            <div className="w-32"></div> {/* Spacer for centering */}
          </div>

          <div className="flex gap-6">
            
            {/* Settings Navigation */}
            <div className={`w-64 p-4 rounded-2xl backdrop-blur-sm ${
              isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
            }`}>
              <nav className="space-y-2">
                {settingsTabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all text-left ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                        : isNightMode 
                          ? 'text-gray-300 hover:text-white hover:bg-gray-700' 
                          : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                    }`}
                  >
                    <tab.icon className="w-5 h-5" />
                    <span className="font-medium">{tab.label}</span>
                  </button>
                ))}
              </nav>
            </div>

            {/* Settings Content */}
            <div className="flex-1">
              <AnimatePresence mode="wait">
                
                {/* Profile Settings */}
                {activeTab === 'profile' && (
                  <motion.div
                    key="profile"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                    className={`p-6 rounded-2xl backdrop-blur-sm ${
                      isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
                    }`}
                  >
                    <h2 className={`text-2xl font-bold mb-6 flex items-center space-x-2 ${
                      isNightMode ? 'text-white' : 'text-gray-800'
                    }`}>
                      <FaUser className="text-blue-500" />
                      <span>Profile Settings</span>
                    </h2>

                    <div className="space-y-6">
                      {/* Username */}
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          isNightMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Username
                        </label>
                        <input
                          type="text"
                          value={userSettings.username}
                          onChange={(e) => handleSettingChange('profile', 'username', e.target.value)}
                          className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                            isNightMode
                              ? 'bg-gray-700 border-gray-600 text-white focus:border-purple-500'
                              : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-purple-500'
                          } focus:outline-none focus:ring-0`}
                        />
                      </div>

                      {/* Email */}
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          isNightMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Email Address
                        </label>
                        <input
                          type="email"
                          value={userSettings.email}
                          onChange={(e) => handleSettingChange('profile', 'email', e.target.value)}
                          className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                            isNightMode
                              ? 'bg-gray-700 border-gray-600 text-white focus:border-purple-500'
                              : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-purple-500'
                          } focus:outline-none focus:ring-0`}
                        />
                      </div>

                      {/* Bio */}
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          isNightMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Bio
                        </label>
                        <textarea
                          rows={3}
                          value={userSettings.bio}
                          onChange={(e) => handleSettingChange('profile', 'bio', e.target.value)}
                          className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                            isNightMode
                              ? 'bg-gray-700 border-gray-600 text-white focus:border-purple-500'
                              : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-purple-500'
                          } focus:outline-none focus:ring-0`}
                        />
                      </div>

                      {/* Clan */}
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          isNightMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Current Clan
                        </label>
                        <select
                          value={userSettings.clan}
                          onChange={(e) => handleSettingChange('profile', 'clan', e.target.value)}
                          className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                            isNightMode
                              ? 'bg-gray-700 border-gray-600 text-white focus:border-purple-500'
                              : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-purple-500'
                          } focus:outline-none focus:ring-0`}
                        >
                          <option value="Naruto">🍥 Naruto</option>
                          <option value="One Piece">🏴‍☠️ One Piece</option>
                          <option value="Demon Slayer">⚔️ Demon Slayer</option>
                          <option value="Attack on Titan">🗡️ Attack on Titan</option>
                          <option value="Dragon Ball">🐉 Dragon Ball</option>
                        </select>
                      </div>

                      {/* Profile Visibility */}
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          isNightMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Profile Visibility
                        </label>
                        <select
                          value={userSettings.profileVisibility}
                          onChange={(e) => handleSettingChange('profile', 'profileVisibility', e.target.value)}
                          className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                            isNightMode
                              ? 'bg-gray-700 border-gray-600 text-white focus:border-purple-500'
                              : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-purple-500'
                          } focus:outline-none focus:ring-0`}
                        >
                          <option value="public">🌍 Public - Anyone can view</option>
                          <option value="friends">👥 Friends Only</option>
                          <option value="private">🔒 Private - Only me</option>
                        </select>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Privacy Settings */}
                {activeTab === 'privacy' && (
                  <motion.div
                    key="privacy"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                    className={`p-6 rounded-2xl backdrop-blur-sm ${
                      isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
                    }`}
                  >
                    <h2 className={`text-2xl font-bold mb-6 flex items-center space-x-2 ${
                      isNightMode ? 'text-white' : 'text-gray-800'
                    }`}>
                      <FaLock className="text-green-500" />
                      <span>Privacy Settings</span>
                    </h2>

                    <div className="space-y-6">
                      {/* Online Status */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Show Online Status
                          </h3>
                          <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Let others see when you're online
                          </p>
                        </div>
                        <button
                          onClick={() => handleSettingChange('privacy', 'showOnlineStatus', !userSettings.showOnlineStatus)}
                          className={`text-2xl transition-colors ${
                            userSettings.showOnlineStatus ? 'text-green-500' : 'text-gray-400'
                          }`}
                        >
                          {userSettings.showOnlineStatus ? <FaToggleOn /> : <FaToggleOff />}
                        </button>
                      </div>

                      {/* Direct Messages */}
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          isNightMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Who can send you direct messages?
                        </label>
                        <select
                          value={userSettings.allowDirectMessages}
                          onChange={(e) => handleSettingChange('privacy', 'allowDirectMessages', e.target.value)}
                          className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                            isNightMode
                              ? 'bg-gray-700 border-gray-600 text-white focus:border-purple-500'
                              : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-purple-500'
                          } focus:outline-none focus:ring-0`}
                        >
                          <option value="everyone">🌍 Everyone</option>
                          <option value="friends">👥 Friends Only</option>
                          <option value="clan">⚔️ Clan Members Only</option>
                          <option value="none">🚫 No One</option>
                        </select>
                      </div>

                      {/* Show Watching List */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Show Watching List
                          </h3>
                          <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Display your anime watching list on your profile
                          </p>
                        </div>
                        <button
                          onClick={() => handleSettingChange('privacy', 'showWatchingList', !userSettings.showWatchingList)}
                          className={`text-2xl transition-colors ${
                            userSettings.showWatchingList ? 'text-green-500' : 'text-gray-400'
                          }`}
                        >
                          {userSettings.showWatchingList ? <FaToggleOn /> : <FaToggleOff />}
                        </button>
                      </div>

                      {/* Show Clan Activity */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Show Clan Activity
                          </h3>
                          <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Display your clan activities and achievements
                          </p>
                        </div>
                        <button
                          onClick={() => handleSettingChange('privacy', 'showClanActivity', !userSettings.showClanActivity)}
                          className={`text-2xl transition-colors ${
                            userSettings.showClanActivity ? 'text-green-500' : 'text-gray-400'
                          }`}
                        >
                          {userSettings.showClanActivity ? <FaToggleOn /> : <FaToggleOff />}
                        </button>
                      </div>

                      {/* Data Collection */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Analytics & Data Collection
                          </h3>
                          <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Help improve AnimeVerse by sharing usage data
                          </p>
                        </div>
                        <button
                          onClick={() => handleSettingChange('privacy', 'dataCollection', !userSettings.dataCollection)}
                          className={`text-2xl transition-colors ${
                            userSettings.dataCollection ? 'text-green-500' : 'text-gray-400'
                          }`}
                        >
                          {userSettings.dataCollection ? <FaToggleOn /> : <FaToggleOff />}
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Notifications Settings */}
                {activeTab === 'notifications' && (
                  <motion.div
                    key="notifications"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                    className={`p-6 rounded-2xl backdrop-blur-sm ${
                      isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
                    }`}
                  >
                    <h2 className={`text-2xl font-bold mb-6 flex items-center space-x-2 ${
                      isNightMode ? 'text-white' : 'text-gray-800'
                    }`}>
                      <FaBell className="text-yellow-500" />
                      <span>Notification Settings</span>
                    </h2>

                    <div className="space-y-6">
                      {/* Email Notifications */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Email Notifications
                          </h3>
                          <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Receive notifications via email
                          </p>
                        </div>
                        <button
                          onClick={() => handleSettingChange('notifications', 'emailNotifications', !userSettings.emailNotifications)}
                          className={`text-2xl transition-colors ${
                            userSettings.emailNotifications ? 'text-green-500' : 'text-gray-400'
                          }`}
                        >
                          {userSettings.emailNotifications ? <FaToggleOn /> : <FaToggleOff />}
                        </button>
                      </div>

                      {/* Push Notifications */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Push Notifications
                          </h3>
                          <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Receive push notifications on your device
                          </p>
                        </div>
                        <button
                          onClick={() => handleSettingChange('notifications', 'pushNotifications', !userSettings.pushNotifications)}
                          className={`text-2xl transition-colors ${
                            userSettings.pushNotifications ? 'text-green-500' : 'text-gray-400'
                          }`}
                        >
                          {userSettings.pushNotifications ? <FaToggleOn /> : <FaToggleOff />}
                        </button>
                      </div>

                      {/* Episode Alerts */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Episode Alerts
                          </h3>
                          <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Get notified when new episodes are released
                          </p>
                        </div>
                        <button
                          onClick={() => handleSettingChange('notifications', 'episodeAlerts', !userSettings.episodeAlerts)}
                          className={`text-2xl transition-colors ${
                            userSettings.episodeAlerts ? 'text-green-500' : 'text-gray-400'
                          }`}
                        >
                          {userSettings.episodeAlerts ? <FaToggleOn /> : <FaToggleOff />}
                        </button>
                      </div>

                      {/* Social Notifications */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Social Notifications
                          </h3>
                          <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Likes, comments, follows, and mentions
                          </p>
                        </div>
                        <button
                          onClick={() => handleSettingChange('notifications', 'socialNotifications', !userSettings.socialNotifications)}
                          className={`text-2xl transition-colors ${
                            userSettings.socialNotifications ? 'text-green-500' : 'text-gray-400'
                          }`}
                        >
                          {userSettings.socialNotifications ? <FaToggleOn /> : <FaToggleOff />}
                        </button>
                      </div>

                      {/* Event Reminders */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Event Reminders
                          </h3>
                          <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Reminders for events you're attending
                          </p>
                        </div>
                        <button
                          onClick={() => handleSettingChange('notifications', 'eventReminders', !userSettings.eventReminders)}
                          className={`text-2xl transition-colors ${
                            userSettings.eventReminders ? 'text-green-500' : 'text-gray-400'
                          }`}
                        >
                          {userSettings.eventReminders ? <FaToggleOn /> : <FaToggleOff />}
                        </button>
                      </div>

                      {/* Marketplace Alerts */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Marketplace Alerts
                          </h3>
                          <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Price drops and new items in your wishlist
                          </p>
                        </div>
                        <button
                          onClick={() => handleSettingChange('notifications', 'marketplaceAlerts', !userSettings.marketplaceAlerts)}
                          className={`text-2xl transition-colors ${
                            userSettings.marketplaceAlerts ? 'text-green-500' : 'text-gray-400'
                          }`}
                        >
                          {userSettings.marketplaceAlerts ? <FaToggleOn /> : <FaToggleOff />}
                        </button>
                      </div>

                      {/* Clan Notifications */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Clan Notifications
                          </h3>
                          <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Clan activities, challenges, and announcements
                          </p>
                        </div>
                        <button
                          onClick={() => handleSettingChange('notifications', 'clanNotifications', !userSettings.clanNotifications)}
                          className={`text-2xl transition-colors ${
                            userSettings.clanNotifications ? 'text-green-500' : 'text-gray-400'
                          }`}
                        >
                          {userSettings.clanNotifications ? <FaToggleOn /> : <FaToggleOff />}
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Appearance Settings */}
                {activeTab === 'appearance' && (
                  <motion.div
                    key="appearance"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                    className={`p-6 rounded-2xl backdrop-blur-sm ${
                      isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
                    }`}
                  >
                    <h2 className={`text-2xl font-bold mb-6 flex items-center space-x-2 ${
                      isNightMode ? 'text-white' : 'text-gray-800'
                    }`}>
                      <FaPalette className="text-purple-500" />
                      <span>Appearance Settings</span>
                    </h2>

                    <div className="space-y-6">
                      {/* Theme */}
                      <div>
                        <label className={`block text-sm font-medium mb-3 ${
                          isNightMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Theme
                        </label>
                        <div className="grid grid-cols-2 gap-4">
                          <button
                            onClick={() => {
                              if (isNightMode) toggleTheme();
                              handleSettingChange('appearance', 'theme', 'light');
                            }}
                            className={`p-4 rounded-lg border-2 transition-all ${
                              !isNightMode
                                ? 'border-purple-500 bg-purple-50'
                                : 'border-gray-600 bg-gray-700 hover:border-gray-500'
                            }`}
                          >
                            <div className="w-full h-16 bg-gradient-to-br from-pink-100 to-blue-100 rounded mb-2"></div>
                            <span className={`font-medium ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                              Light Mode
                            </span>
                          </button>
                          <button
                            onClick={() => {
                              if (!isNightMode) toggleTheme();
                              handleSettingChange('appearance', 'theme', 'dark');
                            }}
                            className={`p-4 rounded-lg border-2 transition-all ${
                              isNightMode
                                ? 'border-purple-500 bg-purple-900/50'
                                : 'border-gray-300 bg-gray-50 hover:border-gray-400'
                            }`}
                          >
                            <div className="w-full h-16 bg-gradient-to-br from-gray-800 to-purple-900 rounded mb-2"></div>
                            <span className={`font-medium ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                              Dark Mode
                            </span>
                          </button>
                        </div>
                      </div>

                      {/* Language */}
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          isNightMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Language
                        </label>
                        <select
                          value={userSettings.language}
                          onChange={(e) => handleSettingChange('appearance', 'language', e.target.value)}
                          className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                            isNightMode
                              ? 'bg-gray-700 border-gray-600 text-white focus:border-purple-500'
                              : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-purple-500'
                          } focus:outline-none focus:ring-0`}
                        >
                          <option value="en">🇺🇸 English</option>
                          <option value="fr">🇫🇷 Français</option>
                        </select>
                      </div>

                      {/* Animations */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Enable Animations
                          </h3>
                          <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Smooth transitions and sakura petal effects
                          </p>
                        </div>
                        <button
                          onClick={() => handleSettingChange('appearance', 'animationsEnabled', !userSettings.animationsEnabled)}
                          className={`text-2xl transition-colors ${
                            userSettings.animationsEnabled ? 'text-green-500' : 'text-gray-400'
                          }`}
                        >
                          {userSettings.animationsEnabled ? <FaToggleOn /> : <FaToggleOff />}
                        </button>
                      </div>

                      {/* Sound Effects */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Sound Effects
                          </h3>
                          <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Button clicks and notification sounds
                          </p>
                        </div>
                        <button
                          onClick={() => handleSettingChange('appearance', 'soundEffects', !userSettings.soundEffects)}
                          className={`text-2xl transition-colors ${
                            userSettings.soundEffects ? 'text-green-500' : 'text-gray-400'
                          }`}
                        >
                          {userSettings.soundEffects ? <FaToggleOn /> : <FaToggleOff />}
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Account Settings */}
                {activeTab === 'account' && (
                  <motion.div
                    key="account"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                    className={`p-6 rounded-2xl backdrop-blur-sm ${
                      isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
                    }`}
                  >
                    <h2 className={`text-2xl font-bold mb-6 flex items-center space-x-2 ${
                      isNightMode ? 'text-white' : 'text-gray-800'
                    }`}>
                      <FaCog className="text-gray-500" />
                      <span>Account Management</span>
                    </h2>

                    <div className="space-y-6">
                      {/* Two-Factor Authentication */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Two-Factor Authentication
                          </h3>
                          <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Add an extra layer of security to your account
                          </p>
                        </div>
                        <button
                          onClick={() => handleSettingChange('account', 'twoFactorAuth', !userSettings.twoFactorAuth)}
                          className={`text-2xl transition-colors ${
                            userSettings.twoFactorAuth ? 'text-green-500' : 'text-gray-400'
                          }`}
                        >
                          {userSettings.twoFactorAuth ? <FaToggleOn /> : <FaToggleOff />}
                        </button>
                      </div>

                      {/* Login Alerts */}
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Login Alerts
                          </h3>
                          <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            Get notified of new login attempts
                          </p>
                        </div>
                        <button
                          onClick={() => handleSettingChange('account', 'loginAlerts', !userSettings.loginAlerts)}
                          className={`text-2xl transition-colors ${
                            userSettings.loginAlerts ? 'text-green-500' : 'text-gray-400'
                          }`}
                        >
                          {userSettings.loginAlerts ? <FaToggleOn /> : <FaToggleOff />}
                        </button>
                      </div>

                      {/* Session Timeout */}
                      <div>
                        <label className={`block text-sm font-medium mb-2 ${
                          isNightMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          Session Timeout (minutes)
                        </label>
                        <select
                          value={userSettings.sessionTimeout}
                          onChange={(e) => handleSettingChange('account', 'sessionTimeout', parseInt(e.target.value))}
                          className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                            isNightMode
                              ? 'bg-gray-700 border-gray-600 text-white focus:border-purple-500'
                              : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-purple-500'
                          } focus:outline-none focus:ring-0`}
                        >
                          <option value={15}>15 minutes</option>
                          <option value={30}>30 minutes</option>
                          <option value={60}>1 hour</option>
                          <option value={120}>2 hours</option>
                          <option value={0}>Never</option>
                        </select>
                      </div>

                      {/* Data Export */}
                      <div className={`p-4 rounded-lg border ${
                        isNightMode ? 'border-gray-600 bg-gray-700/50' : 'border-gray-300 bg-gray-50'
                      }`}>
                        <h3 className={`font-semibold mb-2 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                          Export Your Data
                        </h3>
                        <p className={`text-sm mb-4 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          Download a copy of all your AnimeVerse data including posts, messages, and preferences.
                        </p>
                        <button
                          onClick={handleExportData}
                          className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                        >
                          <FaDownload className="w-4 h-4" />
                          <span>Export Data</span>
                        </button>
                      </div>

                      {/* Delete Account */}
                      <div className={`p-4 rounded-lg border border-red-500 bg-red-500/10`}>
                        <h3 className={`font-semibold mb-2 text-red-500`}>
                          Delete Account
                        </h3>
                        <p className={`text-sm mb-4 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          Permanently delete your account and all associated data. This action cannot be undone.
                        </p>
                        <button
                          onClick={() => setShowDeleteConfirm(true)}
                          className="flex items-center space-x-2 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                        >
                          <FaTrash className="w-4 h-4" />
                          <span>Delete Account</span>
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Admin Panel */}
                {activeTab === 'admin' && isAdmin && (
                  <motion.div
                    key="admin"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                    className={`p-6 rounded-2xl backdrop-blur-sm ${
                      isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
                    }`}
                  >
                    <h2 className={`text-2xl font-bold mb-6 flex items-center space-x-2 ${
                      isNightMode ? 'text-white' : 'text-gray-800'
                    }`}>
                      <FaUserShield className="text-red-500" />
                      <span>Admin Panel</span>
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Content Moderation */}
                      <div className={`p-4 rounded-lg ${
                        isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                      }`}>
                        <h3 className={`font-semibold mb-3 flex items-center space-x-2 ${
                          isNightMode ? 'text-white' : 'text-gray-800'
                        }`}>
                          <FaEye className="text-blue-500" />
                          <span>Content Moderation</span>
                        </h3>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                              Pending Reports
                            </span>
                            <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                              23
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                              Flagged Posts
                            </span>
                            <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">
                              12
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                              Banned Users
                            </span>
                            <span className="bg-gray-500 text-white text-xs px-2 py-1 rounded-full">
                              5
                            </span>
                          </div>
                          <button className="w-full mt-3 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                            Review Reports
                          </button>
                        </div>
                      </div>

                      {/* User Management */}
                      <div className={`p-4 rounded-lg ${
                        isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                      }`}>
                        <h3 className={`font-semibold mb-3 flex items-center space-x-2 ${
                          isNightMode ? 'text-white' : 'text-gray-800'
                        }`}>
                          <FaUser className="text-green-500" />
                          <span>User Management</span>
                        </h3>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                              Total Users
                            </span>
                            <span className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                              {analyticsData.totalUsers.toLocaleString()}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                              Active Today
                            </span>
                            <span className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                              {analyticsData.activeUsers.toLocaleString()}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                              New Today
                            </span>
                            <span className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                              {analyticsData.newUsersToday}
                            </span>
                          </div>
                          <button className="w-full mt-3 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                            Manage Users
                          </button>
                        </div>
                      </div>

                      {/* System Status */}
                      <div className={`p-4 rounded-lg ${
                        isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                      }`}>
                        <h3 className={`font-semibold mb-3 flex items-center space-x-2 ${
                          isNightMode ? 'text-white' : 'text-gray-800'
                        }`}>
                          <FaDatabase className="text-purple-500" />
                          <span>System Status</span>
                        </h3>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                              Server Status
                            </span>
                            <span className="flex items-center space-x-1">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span className="text-sm text-green-500">Online</span>
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                              Database
                            </span>
                            <span className="flex items-center space-x-1">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span className="text-sm text-green-500">Healthy</span>
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                              API Response
                            </span>
                            <span className="text-sm text-green-500">45ms</span>
                          </div>
                          <button className="w-full mt-3 px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                            System Logs
                          </button>
                        </div>
                      </div>

                      {/* Quick Actions */}
                      <div className={`p-4 rounded-lg ${
                        isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                      }`}>
                        <h3 className={`font-semibold mb-3 flex items-center space-x-2 ${
                          isNightMode ? 'text-white' : 'text-gray-800'
                        }`}>
                          <FaCog className="text-orange-500" />
                          <span>Quick Actions</span>
                        </h3>
                        <div className="space-y-2">
                          <button className="w-full px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                            Send Announcement
                          </button>
                          <button className="w-full px-3 py-2 text-sm bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors">
                            Maintenance Mode
                          </button>
                          <button className="w-full px-3 py-2 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
                            Emergency Shutdown
                          </button>
                          <button className="w-full px-3 py-2 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                            Backup Database
                          </button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Analytics Dashboard */}
                {activeTab === 'analytics' && isAdmin && (
                  <motion.div
                    key="analytics"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                    className={`space-y-6`}
                  >
                    <div className={`p-6 rounded-2xl backdrop-blur-sm ${
                      isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
                    }`}>
                      <h2 className={`text-2xl font-bold mb-6 flex items-center space-x-2 ${
                        isNightMode ? 'text-white' : 'text-gray-800'
                      }`}>
                        <FaChartBar className="text-blue-500" />
                        <span>Analytics Dashboard</span>
                      </h2>

                      {/* Key Metrics */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                        <div className={`p-4 rounded-lg ${
                          isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                        }`}>
                          <div className="flex items-center justify-between mb-2">
                            <span className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              Total Users
                            </span>
                            <FaUser className="text-blue-500" />
                          </div>
                          <div className={`text-2xl font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            {analyticsData.totalUsers.toLocaleString()}
                          </div>
                          <div className="text-sm text-green-500">+12.5% from last month</div>
                        </div>

                        <div className={`p-4 rounded-lg ${
                          isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                        }`}>
                          <div className="flex items-center justify-between mb-2">
                            <span className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              Daily Active
                            </span>
                            <FaEye className="text-green-500" />
                          </div>
                          <div className={`text-2xl font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            {analyticsData.engagementMetrics.dailyActiveUsers.toLocaleString()}
                          </div>
                          <div className="text-sm text-green-500">+8.3% from yesterday</div>
                        </div>

                        <div className={`p-4 rounded-lg ${
                          isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                        }`}>
                          <div className="flex items-center justify-between mb-2">
                            <span className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              Total Posts
                            </span>
                            <FaBell className="text-purple-500" />
                          </div>
                          <div className={`text-2xl font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            {analyticsData.totalPosts.toLocaleString()}
                          </div>
                          <div className="text-sm text-green-500">+156 today</div>
                        </div>

                        <div className={`p-4 rounded-lg ${
                          isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                        }`}>
                          <div className="flex items-center justify-between mb-2">
                            <span className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              Avg Session
                            </span>
                            <FaCog className="text-orange-500" />
                          </div>
                          <div className={`text-2xl font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            {analyticsData.engagementMetrics.averageSessionTime}
                          </div>
                          <div className="text-sm text-green-500">+2.1 min from last week</div>
                        </div>
                      </div>

                      {/* Clan Distribution */}
                      <div className={`p-4 rounded-lg mb-6 ${
                        isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                      }`}>
                        <h3 className={`font-semibold mb-4 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                          Clan Distribution
                        </h3>
                        <div className="space-y-3">
                          {analyticsData.clanDistribution.map((clan, index) => (
                            <div key={clan.clan} className="flex items-center space-x-3">
                              <div className="flex-1">
                                <div className="flex items-center justify-between mb-1">
                                  <span className={`text-sm font-medium ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                                    {clan.clan === 'Naruto' ? '🍥' :
                                     clan.clan === 'One Piece' ? '🏴‍☠️' :
                                     clan.clan === 'Demon Slayer' ? '⚔️' :
                                     clan.clan === 'Attack on Titan' ? '🗡️' : '🐉'} {clan.clan}
                                  </span>
                                  <span className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                    {clan.count.toLocaleString()} ({clan.percentage}%)
                                  </span>
                                </div>
                                <div className={`w-full bg-gray-300 rounded-full h-2 ${
                                  isNightMode ? 'bg-gray-600' : 'bg-gray-200'
                                }`}>
                                  <div
                                    className={`h-2 rounded-full ${
                                      index === 0 ? 'bg-orange-500' :
                                      index === 1 ? 'bg-blue-500' :
                                      index === 2 ? 'bg-red-500' :
                                      index === 3 ? 'bg-green-500' : 'bg-purple-500'
                                    }`}
                                    style={{ width: `${clan.percentage}%` }}
                                  ></div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Engagement Metrics */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className={`p-4 rounded-lg ${
                          isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                        }`}>
                          <h3 className={`font-semibold mb-4 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Daily Activity
                          </h3>
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                Posts per Day
                              </span>
                              <span className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                                {analyticsData.engagementMetrics.postsPerDay.toLocaleString()}
                              </span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                Messages per Day
                              </span>
                              <span className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                                {analyticsData.engagementMetrics.messagesPerDay.toLocaleString()}
                              </span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                Events per Week
                              </span>
                              <span className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                                {analyticsData.engagementMetrics.eventsPerWeek}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className={`p-4 rounded-lg ${
                          isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                        }`}>
                          <h3 className={`font-semibold mb-4 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                            Platform Stats
                          </h3>
                          <div className="space-y-3">
                            <div className="flex items-center justify-between">
                              <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                Total Events
                              </span>
                              <span className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                                {analyticsData.totalEvents}
                              </span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                Marketplace Items
                              </span>
                              <span className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                                {analyticsData.totalMarketplaceItems.toLocaleString()}
                              </span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                Active Clans
                              </span>
                              <span className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                                {analyticsData.clanDistribution.length}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          {/* Delete Account Confirmation Modal */}
          <AnimatePresence>
            {showDeleteConfirm && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
                onClick={() => setShowDeleteConfirm(false)}
              >
                <motion.div
                  initial={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.9, opacity: 0 }}
                  className={`p-6 rounded-2xl max-w-md w-full mx-4 ${
                    isNightMode ? 'bg-gray-800' : 'bg-white'
                  }`}
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className="flex items-center space-x-3 mb-4">
                    <FaExclamationTriangle className="text-red-500 text-2xl" />
                    <h3 className={`text-xl font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                      Delete Account
                    </h3>
                  </div>

                  <p className={`mb-6 ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Are you sure you want to delete your account? This will permanently remove all your data, including:
                  </p>

                  <ul className={`mb-6 space-y-1 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    <li>• All posts and comments</li>
                    <li>• Message history</li>
                    <li>• Clan progress and achievements</li>
                    <li>• Marketplace listings and purchases</li>
                    <li>• Event RSVPs and created events</li>
                  </ul>

                  <div className="flex space-x-3">
                    <button
                      onClick={() => setShowDeleteConfirm(false)}
                      className={`flex-1 px-4 py-2 rounded-lg transition-colors ${
                        isNightMode
                          ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleDeleteAccount}
                      className="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                    >
                      Delete Forever
                    </button>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

export default Settings;
