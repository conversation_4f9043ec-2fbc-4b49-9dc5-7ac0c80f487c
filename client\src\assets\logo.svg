<svg width="120" height="40" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Sakura petals background -->
  <circle cx="15" cy="15" r="3" fill="#FFB6C1" opacity="0.6"/>
  <circle cx="105" cy="25" r="2" fill="#FFB6C1" opacity="0.4"/>
  
  <!-- Main logo circle with gradient -->
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ec4899;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Logo symbol -->
  <circle cx="20" cy="20" r="15" fill="url(#logoGradient)"/>
  <path d="M15 20L20 15L25 20L20 25Z" fill="white"/>
  
  <!-- Text -->
  <text x="45" y="16" font-family="Poppins, sans-serif" font-size="14" font-weight="600" fill="#1f2937">Anime</text>
  <text x="45" y="30" font-family="Poppins, sans-serif" font-size="14" font-weight="600" fill="url(#logoGradient)">Verse</text>
</svg>
