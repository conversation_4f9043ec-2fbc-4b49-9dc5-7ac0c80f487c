import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FaArrowLeft, FaCalendarAlt, FaUsers, FaPlay, FaPlus, FaBell,
  FaMapMarkerAlt, FaClock, FaTicketAlt, FaHeart, FaShare,
  FaFilter, FaSearch, FaEye, FaComments, FaStar
} from 'react-icons/fa';
import { useTheme } from '../context/ThemeContext';
import { useNavigate } from 'react-router-dom';

const Festivals = () => {
  const { isNightMode } = useTheme();
  const navigate = useNavigate();
  
  const [activeTab, setActiveTab] = useState('calendar'); // calendar, watch-parties, my-events, create
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewMode, setViewMode] = useState('month'); // month, week, day
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter states
  const [filters, setFilters] = useState({
    eventType: 'all', // all, convention, meetup, watch-party, tournament
    location: 'all', // all, online, local, international
    clan: 'all' // all, naruto, one-piece, demon-slayer, etc.
  });

  // Mock events data
  const events = [
    {
      id: 1,
      title: 'Anime Expo 2024',
      type: 'convention',
      date: '2024-07-04',
      time: '09:00',
      endDate: '2024-07-07',
      endTime: '18:00',
      location: 'Los Angeles Convention Center',
      isOnline: false,
      organizer: {
        username: 'AnimeExpoOfficial',
        verified: true,
        clan: 'Official'
      },
      description: 'The largest anime convention in North America! Join thousands of fans for panels, screenings, and exclusive merchandise.',
      image: '🎪',
      attendees: 45000,
      maxAttendees: 50000,
      price: 89.99,
      tags: ['convention', 'cosplay', 'merchandise', 'panels'],
      rsvpStatus: 'going', // going, interested, not-going
      isWatchParty: false,
      animeTitle: null,
      clan: null
    },
    {
      id: 2,
      title: 'One Piece Episode 1100 Watch Party',
      type: 'watch-party',
      date: '2024-01-20',
      time: '20:00',
      endDate: '2024-01-20',
      endTime: '21:30',
      location: 'Virtual Room #1',
      isOnline: true,
      organizer: {
        username: 'PirateKing_Luffy',
        verified: false,
        clan: 'One Piece'
      },
      description: 'Join fellow Straw Hat crew members as we watch the milestone 1100th episode together! Reactions and discussions welcome!',
      image: '🏴‍☠️',
      attendees: 234,
      maxAttendees: 500,
      price: 0,
      tags: ['watch-party', 'one-piece', 'milestone', 'discussion'],
      rsvpStatus: 'interested',
      isWatchParty: true,
      animeTitle: 'One Piece',
      clan: 'One Piece'
    },
    {
      id: 3,
      title: 'Tokyo Anime Meetup',
      type: 'meetup',
      date: '2024-02-15',
      time: '14:00',
      endDate: '2024-02-15',
      endTime: '18:00',
      location: 'Shibuya Park, Tokyo',
      isOnline: false,
      organizer: {
        username: 'TokyoOtaku_Senpai',
        verified: true,
        clan: 'Naruto'
      },
      description: 'Monthly Tokyo anime fans meetup! Discuss latest episodes, trade merchandise, and make new friends.',
      image: '🗼',
      attendees: 67,
      maxAttendees: 100,
      price: 0,
      tags: ['meetup', 'tokyo', 'social', 'trading'],
      rsvpStatus: 'not-going',
      isWatchParty: false,
      animeTitle: null,
      clan: null
    },
    {
      id: 4,
      title: 'Demon Slayer Tournament',
      type: 'tournament',
      date: '2024-03-10',
      time: '16:00',
      endDate: '2024-03-10',
      endTime: '22:00',
      location: 'Gaming Arena Online',
      isOnline: true,
      organizer: {
        username: 'HashiraMaster',
        verified: true,
        clan: 'Demon Slayer'
      },
      description: 'Competitive Demon Slayer gaming tournament with exclusive prizes! Test your skills against other demon slayers.',
      image: '⚔️',
      attendees: 128,
      maxAttendees: 256,
      price: 15.00,
      tags: ['tournament', 'gaming', 'competitive', 'prizes'],
      rsvpStatus: 'going',
      isWatchParty: false,
      animeTitle: 'Demon Slayer',
      clan: 'Demon Slayer'
    }
  ];

  const eventTypes = [
    { id: 'all', name: 'All Events', icon: '🎪', color: 'purple' },
    { id: 'convention', name: 'Conventions', icon: '🏢', color: 'blue' },
    { id: 'meetup', name: 'Meetups', icon: '👥', color: 'green' },
    { id: 'watch-party', name: 'Watch Parties', icon: '📺', color: 'red' },
    { id: 'tournament', name: 'Tournaments', icon: '🏆', color: 'yellow' }
  ];

  const getRSVPColor = (status) => {
    switch (status) {
      case 'going': return 'text-green-500 bg-green-500/20';
      case 'interested': return 'text-yellow-500 bg-yellow-500/20';
      case 'not-going': return 'text-red-500 bg-red-500/20';
      default: return 'text-gray-500 bg-gray-500/20';
    }
  };

  const getEventTypeColor = (type) => {
    const typeObj = eventTypes.find(t => t.id === type);
    return typeObj ? typeObj.color : 'purple';
  };

  const filteredEvents = events.filter(event => {
    if (searchQuery && !event.title.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    if (filters.eventType !== 'all' && event.type !== filters.eventType) {
      return false;
    }
    if (filters.location !== 'all') {
      if (filters.location === 'online' && !event.isOnline) return false;
      if (filters.location === 'local' && event.isOnline) return false;
    }
    if (filters.clan !== 'all' && event.clan !== filters.clan) {
      return false;
    }
    return true;
  });

  const upcomingEvents = filteredEvents.filter(event => new Date(event.date) >= new Date());
  const watchParties = filteredEvents.filter(event => event.isWatchParty);

  return (
    // 🔧 SCROLLBAR PATTERN - Consistent with all pages
    <div className={`min-h-screen overflow-hidden transition-all duration-300 ${
      isNightMode 
        ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900' 
        : 'bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50'
    }`}>
      <div className="h-screen overflow-y-auto hide-scrollbar">
        
        {/* 🌸 Floating Sakura Petals */}
        <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-pink-300 rounded-full opacity-20"
              style={{
                left: `${Math.random() * 100}%`,
                top: `-10px`
              }}
              animate={{
                y: window.innerHeight + 50,
                x: [0, 30, -30, 0],
                rotate: 360,
                opacity: [0.2, 0.05, 0.2]
              }}
              transition={{
                duration: Math.random() * 3 + 12,
                repeat: Infinity,
                delay: Math.random() * 5,
                ease: "linear"
              }}
            />
          ))}
        </div>

        <div className="relative z-10 max-w-7xl mx-auto p-4">
          
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={() => navigate('/home')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                isNightMode ? 'text-gray-300 hover:text-white hover:bg-gray-700' : 'text-gray-600 hover:text-gray-800 hover:bg-white'
              }`}
            >
              <FaArrowLeft className="w-4 h-4" />
              <span>Back to Home</span>
            </button>
            
            <h1 className={`text-3xl font-bold flex items-center space-x-3 ${
              isNightMode ? 'text-white' : 'text-gray-800'
            }`}>
              <span>🎪</span>
              <span>Festivals</span>
            </h1>

            <div className="flex items-center space-x-2">
              <button
                className={`p-2 rounded-lg transition-colors ${
                  isNightMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <FaBell className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Search and Filters */}
          <div className={`p-6 rounded-2xl mb-6 backdrop-blur-sm ${
            isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
          }`}>
            <div className="flex flex-col md:flex-row gap-4">
              
              {/* Search Bar */}
              <div className="flex-1 relative">
                <FaSearch className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${
                  isNightMode ? 'text-gray-400' : 'text-gray-500'
                }`} />
                <input
                  type="text"
                  placeholder="Search events, conventions, watch parties..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-colors ${
                    isNightMode
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500'
                      : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500'
                  } focus:outline-none focus:ring-0`}
                />
              </div>

              {/* Filter Button */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center space-x-2 px-6 py-3 rounded-lg transition-colors ${
                  showFilters
                    ? 'bg-purple-500 text-white'
                    : isNightMode 
                      ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <FaFilter className="w-4 h-4" />
                <span>Filters</span>
              </button>

              {/* Create Event Button */}
              <button
                onClick={() => setActiveTab('create')}
                className="flex items-center space-x-2 px-6 py-3 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:from-green-600 hover:to-emerald-600 transition-all"
              >
                <FaPlus className="w-4 h-4" />
                <span>Create Event</span>
              </button>
            </div>

            {/* Advanced Filters */}
            <AnimatePresence>
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="mt-6 pt-6 border-t border-gray-300"
                >
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    
                    {/* Event Type Filter */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${
                        isNightMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Event Type
                      </label>
                      <select
                        value={filters.eventType}
                        onChange={(e) => setFilters({...filters, eventType: e.target.value})}
                        className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                          isNightMode
                            ? 'bg-gray-700 border-gray-600 text-white'
                            : 'bg-gray-50 border-gray-300 text-gray-900'
                        } focus:outline-none focus:ring-0 focus:border-purple-500`}
                      >
                        {eventTypes.map(type => (
                          <option key={type.id} value={type.id}>
                            {type.icon} {type.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Location Filter */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${
                        isNightMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Location
                      </label>
                      <select
                        value={filters.location}
                        onChange={(e) => setFilters({...filters, location: e.target.value})}
                        className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                          isNightMode
                            ? 'bg-gray-700 border-gray-600 text-white'
                            : 'bg-gray-50 border-gray-300 text-gray-900'
                        } focus:outline-none focus:ring-0 focus:border-purple-500`}
                      >
                        <option value="all">All Locations</option>
                        <option value="online">🌐 Online Events</option>
                        <option value="local">📍 Local Events</option>
                        <option value="international">✈️ International</option>
                      </select>
                    </div>

                    {/* Clan Filter */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${
                        isNightMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Clan
                      </label>
                      <select
                        value={filters.clan}
                        onChange={(e) => setFilters({...filters, clan: e.target.value})}
                        className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                          isNightMode
                            ? 'bg-gray-700 border-gray-600 text-white'
                            : 'bg-gray-50 border-gray-300 text-gray-900'
                        } focus:outline-none focus:ring-0 focus:border-purple-500`}
                      >
                        <option value="all">All Clans</option>
                        <option value="Naruto">🍥 Naruto</option>
                        <option value="One Piece">🏴‍☠️ One Piece</option>
                        <option value="Demon Slayer">⚔️ Demon Slayer</option>
                        <option value="Attack on Titan">🗡️ Attack on Titan</option>
                      </select>
                    </div>
                  </div>

                  {/* Clear Filters */}
                  <div className="mt-4 flex justify-end">
                    <button
                      onClick={() => setFilters({
                        eventType: 'all',
                        location: 'all',
                        clan: 'all'
                      })}
                      className={`px-4 py-2 rounded-lg text-sm transition-colors ${
                        isNightMode 
                          ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700' 
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      Clear All Filters
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Event Type Categories */}
          <div className={`p-4 rounded-2xl mb-6 backdrop-blur-sm ${
            isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
          }`}>
            <div className="flex space-x-2 overflow-x-auto hide-scrollbar pb-2">
              {eventTypes.map((type) => (
                <button
                  key={type.id}
                  onClick={() => setFilters({...filters, eventType: type.id})}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-all ${
                    filters.eventType === type.id
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                      : isNightMode 
                        ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <span className="text-lg">{type.icon}</span>
                  <span className="font-medium">{type.name}</span>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    filters.eventType === type.id
                      ? 'bg-white/20 text-white'
                      : isNightMode ? 'bg-gray-600 text-gray-300' : 'bg-gray-200 text-gray-600'
                  }`}>
                    {type.id === 'all' ? events.length : events.filter(e => e.type === type.id).length}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className={`flex space-x-1 mb-6 p-1 rounded-xl ${
            isNightMode ? 'bg-gray-800/50' : 'bg-white/50'
          }`}>
            {[
              { id: 'calendar', label: 'Calendar', icon: FaCalendarAlt },
              { id: 'watch-parties', label: 'Watch Parties', icon: FaPlay },
              { id: 'my-events', label: 'My Events', icon: FaTicketAlt },
              { id: 'create', label: 'Create Event', icon: FaPlus }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                    : isNightMode 
                      ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700/50' 
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span className="font-medium">{tab.label}</span>
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <AnimatePresence mode="wait">
            {activeTab === 'calendar' && (
              <motion.div
                key="calendar"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <EventGrid events={upcomingEvents} isNightMode={isNightMode} />
              </motion.div>
            )}

            {activeTab === 'watch-parties' && (
              <motion.div
                key="watch-parties"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="mb-6">
                  <h2 className={`text-2xl font-bold flex items-center space-x-2 ${
                    isNightMode ? 'text-white' : 'text-gray-800'
                  }`}>
                    <FaPlay className="text-red-500" />
                    <span>Virtual Watch Parties</span>
                  </h2>
                  <p className={`text-sm mt-1 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    Join group viewing sessions with fellow anime fans
                  </p>
                </div>
                <EventGrid events={watchParties} isNightMode={isNightMode} />
              </motion.div>
            )}

            {activeTab === 'my-events' && (
              <motion.div
                key="my-events"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="mb-6">
                  <h2 className={`text-2xl font-bold flex items-center space-x-2 ${
                    isNightMode ? 'text-white' : 'text-gray-800'
                  }`}>
                    <FaTicketAlt className="text-blue-500" />
                    <span>My Events</span>
                  </h2>
                  <p className={`text-sm mt-1 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    Events you're attending or have created
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                  {/* Going Events */}
                  <div className={`p-6 rounded-xl backdrop-blur-sm ${
                    isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
                  }`}>
                    <h3 className={`text-lg font-bold mb-4 flex items-center space-x-2 ${
                      isNightMode ? 'text-white' : 'text-gray-800'
                    }`}>
                      <span className="w-3 h-3 bg-green-500 rounded-full"></span>
                      <span>Going ({events.filter(e => e.rsvpStatus === 'going').length})</span>
                    </h3>
                    <div className="space-y-3">
                      {events.filter(e => e.rsvpStatus === 'going').map((event) => (
                        <div key={event.id} className={`p-3 rounded-lg ${
                          isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                        }`}>
                          <div className="flex items-center space-x-3">
                            <span className="text-2xl">{event.image}</span>
                            <div className="flex-1">
                              <h4 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                                {event.title}
                              </h4>
                              <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                {new Date(event.date).toLocaleDateString()} at {event.time}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Interested Events */}
                  <div className={`p-6 rounded-xl backdrop-blur-sm ${
                    isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
                  }`}>
                    <h3 className={`text-lg font-bold mb-4 flex items-center space-x-2 ${
                      isNightMode ? 'text-white' : 'text-gray-800'
                    }`}>
                      <span className="w-3 h-3 bg-yellow-500 rounded-full"></span>
                      <span>Interested ({events.filter(e => e.rsvpStatus === 'interested').length})</span>
                    </h3>
                    <div className="space-y-3">
                      {events.filter(e => e.rsvpStatus === 'interested').map((event) => (
                        <div key={event.id} className={`p-3 rounded-lg ${
                          isNightMode ? 'bg-gray-700/50' : 'bg-gray-50'
                        }`}>
                          <div className="flex items-center space-x-3">
                            <span className="text-2xl">{event.image}</span>
                            <div className="flex-1">
                              <h4 className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                                {event.title}
                              </h4>
                              <p className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                {new Date(event.date).toLocaleDateString()} at {event.time}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {activeTab === 'create' && (
              <motion.div
                key="create"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <CreateEventForm isNightMode={isNightMode} />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

// Event Grid Component
const EventGrid = ({ events, isNightMode }) => {
  const getRSVPColor = (status) => {
    switch (status) {
      case 'going': return 'text-green-500 bg-green-500/20';
      case 'interested': return 'text-yellow-500 bg-yellow-500/20';
      case 'not-going': return 'text-red-500 bg-red-500/20';
      default: return 'text-gray-500 bg-gray-500/20';
    }
  };

  const formatPrice = (price) => {
    return price === 0 ? 'Free' : new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {events.map((event, index) => (
        <motion.div
          key={event.id}
          className={`p-6 rounded-xl backdrop-blur-sm transition-all hover:scale-105 cursor-pointer ${
            isNightMode ? 'bg-gray-800/90 hover:bg-gray-700/90' : 'bg-white/90 hover:bg-gray-50/90'
          }`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <div className="relative mb-4">
            <div className="w-full h-32 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-6xl shadow-lg">
              {event.image}
            </div>
            <div className={`absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-bold ${
              event.isOnline ? 'bg-blue-500 text-white' : 'bg-green-500 text-white'
            }`}>
              {event.isOnline ? 'ONLINE' : 'IN-PERSON'}
            </div>
            {event.price === 0 && (
              <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                FREE
              </div>
            )}
          </div>

          <h3 className={`font-bold text-lg mb-2 line-clamp-2 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
            {event.title}
          </h3>

          <div className="flex items-center space-x-2 mb-2">
            <FaCalendarAlt className={`w-4 h-4 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`} />
            <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
              {new Date(event.date).toLocaleDateString()}
            </span>
            <FaClock className={`w-4 h-4 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`} />
            <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
              {event.time}
            </span>
          </div>

          <div className="flex items-center space-x-2 mb-3">
            <FaMapMarkerAlt className={`w-4 h-4 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`} />
            <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
              {event.location}
            </span>
          </div>

          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <FaUsers className={`w-4 h-4 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`} />
              <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {event.attendees}/{event.maxAttendees}
              </span>
            </div>
            <div className={`text-lg font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
              {formatPrice(event.price)}
            </div>
          </div>

          <p className={`text-sm mb-4 line-clamp-2 ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
            {event.description}
          </p>

          <div className="flex items-center justify-between">
            <div className={`px-3 py-1 rounded-full text-xs font-medium ${getRSVPColor(event.rsvpStatus)}`}>
              {event.rsvpStatus.toUpperCase()}
            </div>

            <div className="flex items-center space-x-2">
              <button className={`p-2 rounded-lg transition-colors ${
                isNightMode ? 'text-gray-400 hover:text-red-500' : 'text-gray-500 hover:text-red-500'
              }`}>
                <FaHeart className="w-4 h-4" />
              </button>
              <button className={`p-2 rounded-lg transition-colors ${
                isNightMode ? 'text-gray-400 hover:text-blue-500' : 'text-gray-500 hover:text-blue-500'
              }`}>
                <FaShare className="w-4 h-4" />
              </button>
            </div>
          </div>

          <div className="mt-4 flex space-x-2">
            <button className="flex-1 bg-purple-500 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-purple-600 transition-colors">
              View Details
            </button>
            <button className="px-3 py-2 border border-green-500 text-green-500 rounded-lg text-sm hover:bg-green-500 hover:text-white transition-colors">
              RSVP
            </button>
          </div>
        </motion.div>
      ))}
    </div>
  );
};

// Create Event Form Component
const CreateEventForm = ({ isNightMode }) => {
  const [formData, setFormData] = useState({
    title: '',
    type: 'meetup',
    date: '',
    time: '',
    endDate: '',
    endTime: '',
    location: '',
    isOnline: false,
    description: '',
    maxAttendees: 50,
    price: 0,
    animeTitle: '',
    clan: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Creating event:', formData);
    // Handle event creation
  };

  return (
    <div className={`p-6 rounded-xl backdrop-blur-sm ${
      isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
    }`}>
      <h2 className={`text-2xl font-bold mb-6 flex items-center space-x-2 ${
        isNightMode ? 'text-white' : 'text-gray-800'
      }`}>
        <FaPlus className="text-green-500" />
        <span>Create New Event</span>
      </h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Event Title */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isNightMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              Event Title *
            </label>
            <input
              type="text"
              required
              value={formData.title}
              onChange={(e) => setFormData({...formData, title: e.target.value})}
              placeholder="Enter event title..."
              className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                isNightMode
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                  : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'
              } focus:outline-none focus:ring-0 focus:border-purple-500`}
            />
          </div>

          {/* Event Type */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isNightMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              Event Type *
            </label>
            <select
              required
              value={formData.type}
              onChange={(e) => setFormData({...formData, type: e.target.value})}
              className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                isNightMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-gray-50 border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-0 focus:border-purple-500`}
            >
              <option value="meetup">👥 Meetup</option>
              <option value="watch-party">📺 Watch Party</option>
              <option value="tournament">🏆 Tournament</option>
              <option value="convention">🏢 Convention</option>
            </select>
          </div>

          {/* Start Date */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isNightMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              Start Date *
            </label>
            <input
              type="date"
              required
              value={formData.date}
              onChange={(e) => setFormData({...formData, date: e.target.value})}
              className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                isNightMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-gray-50 border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-0 focus:border-purple-500`}
            />
          </div>

          {/* Start Time */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isNightMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              Start Time *
            </label>
            <input
              type="time"
              required
              value={formData.time}
              onChange={(e) => setFormData({...formData, time: e.target.value})}
              className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                isNightMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-gray-50 border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-0 focus:border-purple-500`}
            />
          </div>
        </div>

        {/* Location */}
        <div>
          <label className={`block text-sm font-medium mb-2 ${
            isNightMode ? 'text-gray-300' : 'text-gray-700'
          }`}>
            Location *
          </label>
          <div className="flex items-center space-x-4 mb-2">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={formData.isOnline}
                onChange={(e) => setFormData({...formData, isOnline: e.target.checked})}
                className="rounded text-purple-500 focus:ring-purple-500"
              />
              <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Online Event
              </span>
            </label>
          </div>
          <input
            type="text"
            required
            value={formData.location}
            onChange={(e) => setFormData({...formData, location: e.target.value})}
            placeholder={formData.isOnline ? "Virtual room or platform..." : "Physical address or venue..."}
            className={`w-full px-3 py-2 rounded-lg border transition-colors ${
              isNightMode
                ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'
            } focus:outline-none focus:ring-0 focus:border-purple-500`}
          />
        </div>

        {/* Description */}
        <div>
          <label className={`block text-sm font-medium mb-2 ${
            isNightMode ? 'text-gray-300' : 'text-gray-700'
          }`}>
            Description *
          </label>
          <textarea
            required
            rows={4}
            value={formData.description}
            onChange={(e) => setFormData({...formData, description: e.target.value})}
            placeholder="Describe your event..."
            className={`w-full px-3 py-2 rounded-lg border transition-colors ${
              isNightMode
                ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'
            } focus:outline-none focus:ring-0 focus:border-purple-500`}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Max Attendees */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isNightMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              Max Attendees
            </label>
            <input
              type="number"
              min="1"
              value={formData.maxAttendees}
              onChange={(e) => setFormData({...formData, maxAttendees: parseInt(e.target.value)})}
              className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                isNightMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-gray-50 border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-0 focus:border-purple-500`}
            />
          </div>

          {/* Price */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isNightMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              Price (USD)
            </label>
            <input
              type="number"
              min="0"
              step="0.01"
              value={formData.price}
              onChange={(e) => setFormData({...formData, price: parseFloat(e.target.value)})}
              placeholder="0.00 for free events"
              className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                isNightMode
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                  : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'
              } focus:outline-none focus:ring-0 focus:border-purple-500`}
            />
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            className={`px-6 py-3 rounded-lg transition-colors ${
              isNightMode
                ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
            }`}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg font-semibold hover:from-green-600 hover:to-emerald-600 transition-all"
          >
            Create Event
          </button>
        </div>
      </form>
    </div>
  );
};

export default Festivals;
