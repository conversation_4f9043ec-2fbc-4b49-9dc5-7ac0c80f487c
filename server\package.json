{"name": "animeverse-server", "version": "1.0.0", "description": "AnimeVerse backend server - Social platform for anime fans", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "dev:full": "concurrently \"npm run dev\" \"cd ../client && npm run dev\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["anime", "social", "express", "nodejs", "postgresql"], "author": "AnimeVerse Team", "license": "MIT", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.16.3", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3"}}