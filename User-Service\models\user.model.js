const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const UserSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,  // This creates an index automatically
    trim: true,
    minlength: 3,
    maxlength: 30,
    index: true    // Remove this line to avoid duplicate index
  },
  email: {
    type: String,
    required: true,
    unique: true,  // This creates an index automatically
    trim: true,
    lowercase: true,
    match: [/\S+@\S+\.\S+/, 'is invalid'],
    index: true    // Remove this line to avoid duplicate index
  },
  password: {
    type: String,
    required: true,
    minlength: 6,
    select: false  // Never return password in queries
  },
  avatar: {
    type: String,
    default: 'default-avatar.jpg'
  },
  bio: {
    type: String,
    maxlength: 500,
    default: ''
  },
  favoriteAnime: {
    type: [String],
    default: []
  },
  mainCommunity: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Community',
    index: true
  },
  joinedCommunities: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Community'
  }],
  followers: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  following: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  xp: {
    type: Number,
    default: 0
  },
  level: {
    type: Number,
    default: 1
  },
  badges: [{
    type: String
  }],
  settings: {
    language: {
      type: String,
      enum: ['en', 'fr'],
      default: 'en'
    },
    theme: {
      type: String,
      enum: ['light', 'dark'],
      default: 'dark'
    },
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      push: {
        type: Boolean,
        default: true
      }
    }
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  isPremium: {
    type: Boolean,
    default: false,
    index: true
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for follower count
UserSchema.virtual('followerCount').get(function() {
  return this.followers?.length || 0;
});

// Virtual for following count
UserSchema.virtual('followingCount').get(function() {
  return this.following?.length || 0;
});

// Hash password before saving
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (err) {
    next(err);
  }
});

// Method to compare passwords
UserSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Method to update user profile
UserSchema.methods.updateProfile = async function(updateData) {
  const allowedUpdates = ['username', 'avatar', 'bio', 'favoriteAnime', 'settings'];
  Object.keys(updateData).forEach(key => {
    if (allowedUpdates.includes(key)) {
      this[key] = updateData[key];
    }
  });
  this.updatedAt = Date.now();
  await this.save();
  return this;
};

// Method to add XP
UserSchema.methods.addXP = async function(amount) {
  this.xp += amount;
  // Level up every 100 XP
  while (this.xp >= 100) {
    this.level += 1;
    this.xp -= 100;
  }
  this.updatedAt = Date.now();
  await this.save();
  return this;
};

// Static method to find user by email
UserSchema.statics.findByEmail = async function(email) {
  return await this.findOne({ email }).select('-password');
};

// Static method to search users
UserSchema.statics.searchUsers = async function(query) {
  return await this.find({
    $or: [
      { username: { $regex: query, $options: 'i' }},
      { email: { $regex: query, $options: 'i' }}
    ]
  }).select('username avatar bio followerCount followingCount');
};

// Compound indexes (only add these if you need them for query performance)
UserSchema.index({ followers: 1 });
UserSchema.index({ following: 1 });
UserSchema.index({ joinedCommunities: 1 });
UserSchema.index({ isPremium: 1, level: -1 }); // For leaderboards

module.exports = mongoose.model('User', UserSchema);