import React, { useState } from 'react';
import { FaEnvelope, FaArrowLeft, FaExclamationTriangle, FaCheckCircle, FaKey, <PERSON>a<PERSON>ye, FaEyeSlash } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { validateEmail } from '../../utils/helper';
import Toast from '../../components/messages/Toast';
import axiosCookies from '../../utils/axiosCookies';
import { useTheme } from '../../context/ThemeContext';

const ForgotPassword = () => {
  const { isNightMode } = useTheme();
  const [step, setStep] = useState(1); // 1: Email, 2: Code, 3: New Password
  const [email, setEmail] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const navigate = useNavigate();

  const handleSendCode = async (e) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    if (!validateEmail(email)) {
      setError("Please enter a valid email address");
      setIsLoading(false);
      return;
    }

    try {
      const response = await axiosCookies.post('/auth/forgot-password', {
        email: email.trim()
      });

      if (response.data.success) {
        setSuccess("Verification code sent to your email!");
        setStep(2);
      }
    } catch (error) {
      setError(error.response?.data?.message || "Failed to send verification code. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyCode = async (e) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    if (!verificationCode.trim()) {
      setError("Please enter the verification code");
      setIsLoading(false);
      return;
    }

    try {
      const response = await axiosCookies.post('/auth/verify-reset-code', {
        email: email.trim(),
        code: verificationCode.trim()
      });

      if (response.data.success) {
        setSuccess("Code verified! Now set your new password.");
        setStep(3);
      }
    } catch (error) {
      setError(error.response?.data?.message || "Invalid verification code. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async (e) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    if (newPassword.length < 8) {
      setError("Password must be at least 8 characters long");
      setIsLoading(false);
      return;
    }

    if (newPassword !== confirmPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }

    try {
      const response = await axiosCookies.post('/auth/reset-password', {
        email: email.trim(),
        code: verificationCode.trim(),
        newPassword
      });

      if (response.data.success) {
        setToastMessage("🎉 Password reset successful! You can now login.");
        setTimeout(() => {
          navigate('/login');
        }, 2000);
      }
    } catch (error) {
      setError(error.response?.data?.message || "Failed to reset password. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <motion.form
            onSubmit={handleSendCode}
            className="space-y-4"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <div className="text-center mb-6">
              <h2 className={`text-xl md:text-2xl font-bold mb-2 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                Forgot Password? 🔐
              </h2>
              <p className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Enter your email to receive a verification code
              </p>
            </div>

            <div className="relative" style={{ minHeight: '48px' }}>
              <FaEnvelope className={`absolute left-3 top-2/5 transform -translate-y-1/2 text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`} />
              <input
                type="email"
                placeholder="Enter your email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={`
                  w-full pl-10 pr-3 py-2 rounded-lg border transition-colors duration-200 text-sm
                  ${isNightMode
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500'
                    : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500'
                  }
                  focus:outline-none focus:ring-0 box-border
                `}
                style={{ height: '40px', minHeight: '40px', maxHeight: '40px' }}
                required
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className={`
                w-full py-2.5 rounded-lg font-semibold text-white transition-all duration-300 mt-4 text-sm
                bg-gradient-to-r from-purple-500 to-pink-500 hover:from-pink-500 hover:to-purple-500
                hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed
                ${isLoading ? 'animate-pulse' : ''}
              `}
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <motion.div
                    className="w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  />
                  Sending Code...
                </span>
              ) : (
                '🌸 SEND VERIFICATION CODE'
              )}
            </button>
          </motion.form>
        );

      case 2:
        return (
          <motion.form
            onSubmit={handleVerifyCode}
            className="space-y-4"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <div className="text-center mb-6">
              <h2 className={`text-xl md:text-2xl font-bold mb-2 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                Enter Verification Code 📧
              </h2>
              <p className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                We sent a code to <span className="font-semibold text-purple-500">{email}</span>
              </p>
            </div>

            <div className="relative" style={{ minHeight: '48px' }}>
              <FaKey className={`absolute left-3 top-2/5 transform -translate-y-1/2 text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`} />
              <input
                type="text"
                placeholder="Enter 6-digit verification code"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                maxLength={6}
                className={`
                  w-full pl-10 pr-3 py-2 rounded-lg border transition-colors duration-200 text-sm text-center tracking-widest
                  ${isNightMode
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500'
                    : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500'
                  }
                  focus:outline-none focus:ring-0 box-border
                `}
                style={{ height: '40px', minHeight: '40px', maxHeight: '40px' }}
                required
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className={`
                w-full py-2.5 rounded-lg font-semibold text-white transition-all duration-300 mt-4 text-sm
                bg-gradient-to-r from-purple-500 to-pink-500 hover:from-pink-500 hover:to-purple-500
                hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed
                ${isLoading ? 'animate-pulse' : ''}
              `}
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <motion.div
                    className="w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  />
                  Verifying...
                </span>
              ) : (
                '🔑 VERIFY CODE'
              )}
            </button>

            <button
              type="button"
              onClick={() => setStep(1)}
              className={`w-full text-sm ${isNightMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'} transition-colors`}
            >
              ← Back to email entry
            </button>
          </motion.form>
        );

      case 3:
        return (
          <motion.form
            onSubmit={handleResetPassword}
            className="space-y-4"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <div className="text-center mb-6">
              <h2 className={`text-xl md:text-2xl font-bold mb-2 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                Set New Password 🔒
              </h2>
              <p className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Create a strong password for your account
              </p>
            </div>

            <div className="relative" style={{ minHeight: '48px' }}>
              <FaKey className={`absolute left-3 top-2/5 transform -translate-y-1/2 text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`} />
              <input
                type={showPassword ? "text" : "password"}
                placeholder="New password (8+ characters)"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className={`
                  w-full pl-10 pr-10 py-2 rounded-lg border transition-colors duration-200 text-sm
                  ${isNightMode
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500'
                    : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500'
                  }
                  focus:outline-none focus:ring-0 box-border
                `}
                style={{ height: '40px', minHeight: '40px', maxHeight: '40px' }}
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className={`absolute right-2.5 top-2/5 transform -translate-y-1/2 text-sm ${isNightMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'}`}
              >
                {showPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>

            <div className="relative" style={{ minHeight: '48px' }}>
              <FaKey className={`absolute left-3 top-2/5 transform -translate-y-1/2 text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`} />
              <input
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Confirm new password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className={`
                  w-full pl-10 pr-10 py-2 rounded-lg border transition-colors duration-200 text-sm
                  ${isNightMode
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500'
                    : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500'
                  }
                  focus:outline-none focus:ring-0 box-border
                `}
                style={{ height: '40px', minHeight: '40px', maxHeight: '40px' }}
                required
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className={`absolute right-2.5 top-2/5 transform -translate-y-1/2 text-sm ${isNightMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'}`}
              >
                {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className={`
                w-full py-2.5 rounded-lg font-semibold text-white transition-all duration-300 mt-4 text-sm
                bg-gradient-to-r from-purple-500 to-pink-500 hover:from-pink-500 hover:to-purple-500
                hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed
                ${isLoading ? 'animate-pulse' : ''}
              `}
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <motion.div
                    className="w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  />
                  Resetting Password...
                </span>
              ) : (
                '🌸 RESET PASSWORD'
              )}
            </button>
          </motion.form>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`min-h-screen flex items-center justify-center p-4 transition-all duration-300 ${
      isNightMode
        ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-pink-900'
        : 'bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500'
    }`}>
      
      {/* Floating Sakura Petals - Contained Animation */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-pink-300 rounded-full opacity-30"
            style={{
              left: `${Math.random() * 100}%`,
              top: `-10px`
            }}
            animate={{
              y: window.innerHeight + 50,
              x: [0, 30, -30, 0],
              rotate: 360,
              opacity: [0.3, 0.1, 0.3]
            }}
            transition={{
              duration: Math.random() * 3 + 8,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "linear"
            }}
          />
        ))}
      </div>

      <AnimatePresence>
        {error && (
          <motion.div
            className='fixed top-5 left-1/2 transform -translate-x-1/2 bg-red-500 text-white p-4 rounded-lg shadow-xl z-50 max-w-md'
            initial={{ opacity: 0, y: -50, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -50, scale: 0.9 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-center">
              <FaExclamationTriangle className="mr-2" />
              <span>{error}</span>
            </div>
          </motion.div>
        )}

        {success && (
          <motion.div
            className='fixed top-5 left-1/2 transform -translate-x-1/2 bg-green-500 text-white p-4 rounded-lg shadow-xl z-50 max-w-md'
            initial={{ opacity: 0, y: -50, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -50, scale: 0.9 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-center">
              <FaCheckCircle className="mr-2" />
              <span>{success}</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div
        className={`
          w-full max-w-md mx-auto rounded-xl shadow-2xl overflow-hidden backdrop-blur-sm
          ${isNightMode ? 'bg-gray-800/95' : 'bg-white/95'}
        `}
        initial={{ opacity: 0, scale: 0.95, y: 10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        <div className={`p-6 ${isNightMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'}`}>
          
          {/* Back Button */}
          <button
            onClick={() => navigate('/login')}
            className={`flex items-center space-x-2 mb-6 text-sm transition-colors ${
              isNightMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <FaArrowLeft className="w-4 h-4" />
            <span>Back to Login</span>
          </button>

          {/* Progress Indicator */}
          <div className="flex items-center justify-center mb-6">
            {[1, 2, 3].map((stepNumber) => (
              <div key={stepNumber} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold ${
                  step >= stepNumber 
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white' 
                    : isNightMode ? 'bg-gray-600 text-gray-400' : 'bg-gray-200 text-gray-500'
                }`}>
                  {stepNumber}
                </div>
                {stepNumber < 3 && (
                  <div className={`w-8 h-1 mx-2 ${
                    step > stepNumber 
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500' 
                      : isNightMode ? 'bg-gray-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>

          <AnimatePresence mode="wait">
            {renderStep()}
          </AnimatePresence>
        </div>
      </motion.div>

      <Toast
        message={toastMessage}
        type={toastMessage.includes('🎉') ? "success" : "info"}
        onClose={() => setToastMessage("")}
      />
    </div>
  );
};

export default ForgotPassword;
