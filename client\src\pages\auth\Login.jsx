import React, { useState, useEffect } from 'react';
import { FaGoogle, FaFacebook, FaTwitter, FaDiscord, FaEnvelope, FaLock, FaEye, FaEyeSlash, FaExclamationTriangle } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { validateEmail, validatePassword } from '../../utils/helper';
import Toast from '../../components/messages/Toast';
import axiosCookies from '../../utils/axiosCookies';
import { useTheme } from '../../context/ThemeContext';
import { assets } from '../../assets';

const Login = () => {
  const { isNightMode } = useTheme();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState(null);
  const [toastMessage, setToastMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleLogin = async (e) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    // Validation
    if (!validateEmail(email)) {
      setError("Please enter a valid email address");
      setIsLoading(false);
      return;
    }
    if (!validatePassword(password)) {
      setError("Password must be at least 8 characters with mixed case and numbers");
      setIsLoading(false);
      return;
    }

    try {
      const response = await axiosCookies.post('/auth/login', {
        email: email.trim(),
        password
      });

      if (response.data.success) {
        setToastMessage("🎉 Welcome back to AnimeVerse!");
        setTimeout(() => {
          navigate('/welcome');
        }, 1500);
      }
    } catch (error) {
      setError(error.response?.data?.message || "Login failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialLogin = (provider) => {
    setToastMessage(`🚀 ${provider} login coming soon!`);
  };

  return (
    <div className={`min-h-screen flex items-center justify-center p-4 ${
      isNightMode
        ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-pink-900'
        : 'bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500'
    }`}
    >
      {/* Floating Sakura Petals - Contained Animation */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-pink-300 rounded-full opacity-30"
            style={{
              left: `${Math.random() * 100}%`,
              top: `-10px`
            }}
            animate={{
              y: window.innerHeight + 50,
              x: [0, 30, -30, 0],
              rotate: 360,
              opacity: [0.3, 0.1, 0.3]
            }}
            transition={{
              duration: Math.random() * 3 + 8,
              repeat: Infinity,
              delay: Math.random() * 5,
              ease: "linear"
            }}
          />
        ))}
      </div>

      <AnimatePresence>
        {error && (
          <motion.div
            className='fixed top-5 left-1/2 transform -translate-x-1/2 bg-red-500 text-white p-4 rounded-lg shadow-xl z-50 max-w-md'
            initial={{ opacity: 0, y: -50, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -50, scale: 0.9 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-center">
              <FaExclamationTriangle className="mr-2" />
              <span>{error}</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div
        className={`
          w-full max-w-xl mx-auto rounded-xl shadow-2xl overflow-hidden
          flex flex-col md:flex-row backdrop-blur-sm
          h-auto max-h-[90vh]
          ${isNightMode ? 'bg-gray-800/95' : 'bg-white'}
        `}
        // bg-white/95
        style={{ minHeight: '400px' }}
        initial={{ opacity: 0, scale: 0.95, y: 10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        {/* Left Panel - Login Form */}
        <div className={`
          w-full md:w-3/5 p-3 md:p-4 flex flex-col justify-center overflow-y-auto
          ${isNightMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'}
        `}
        style={{ minHeight: '400px' }}
        >
          {/* Form Header */}
          <div className="text-center mb-3" style={{ minHeight: '50px' }}>
            <h2 className={`text-lg md:text-xl font-bold mb-1 ${isNightMode ? 'text-white' : 'text-gray-800'}`} style={{ lineHeight: '1.2' }}>
              Welcome Back
            </h2>
            <p className={`text-xs ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`} style={{ lineHeight: '1.2' }}>
              Sign in to your account! ✨
            </p>
          </div>

          {/* Social Login Buttons */}
          <div className="mb-4 relative z-10">
            <p className={`text-center text-xs font-medium mb-3 ${isNightMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Quick Sign In With
            </p>
            <div className="flex justify-center gap-3">
              <button
                onClick={() => handleSocialLogin('Google')}
                className="w-8 h-8 bg-red-500 text-white rounded-lg flex items-center justify-center hover:bg-red-600 transition-all"
              >
                <FaGoogle className="text-xs" />
              </button>
              <button
                onClick={() => handleSocialLogin('Facebook')}
                className="w-8 h-8 bg-blue-600 text-white rounded-lg flex items-center justify-center hover:bg-blue-700 transition-all"
              >
                <FaFacebook className="text-xs" />
              </button>
              <button
                onClick={() => handleSocialLogin('Twitter')}
                className="w-8 h-8 bg-blue-400 text-white rounded-lg flex items-center justify-center hover:bg-blue-500 transition-all"
              >
                <FaTwitter className="text-xs" />
              </button>
              <button
                onClick={() => handleSocialLogin('Discord')}
                className="w-8 h-8 bg-indigo-600 text-white rounded-lg flex items-center justify-center hover:bg-indigo-700 transition-all"
              >
                <FaDiscord className="text-xs" />
              </button>
            </div>
          </div>

          {/* Divider */}
          <div className="relative text-center mb-3">
            <span className={`px-2 text-xs ${isNightMode ? 'text-gray-400 bg-gray-800' : 'text-gray-500 bg-white'}`}>
              OR
            </span>
            <div className={`absolute top-1/2 left-0 right-0 h-px ${isNightMode ? 'bg-gray-600' : 'bg-gray-300'}`}></div>
          </div>

          {/* Login Form */}
          <form
            onSubmit={handleLogin}
            className="space-y-2 will-change-auto"
            style={{ minHeight: '200px' }}
          >
            {/* Email */}
            <div className="relative" style={{ minHeight: '48px' }}>
              <FaEnvelope className={`absolute left-3 top-2/5  transform -translate-y-1/2 text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`} />
              <input
                type="email"
                placeholder="Email Address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={`
                  w-full pl-10 pr-3 py-2 rounded-lg border transition-colors duration-200 text-sm
                  ${isNightMode
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500'
                    : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500'
                  }
                  focus:outline-none focus:ring-0 box-border
                `}
                style={{ height: '40px', minHeight: '40px', maxHeight: '40px' }}
                required
              />
            </div>

            {/* Password */}
            <div className="relative" style={{ minHeight: '48px' }}>
              <FaLock className={`absolute left-3 top-2/5  transform -translate-y-1/2 text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`} />
              <input
                type={showPassword ? "text" : "password"}
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className={`
                  w-full pl-10 pr-10 py-2 rounded-lg border transition-colors duration-200 text-sm
                  ${isNightMode
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500'
                    : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500'
                  }
                  focus:outline-none focus:ring-0 box-border
                `}
                style={{ height: '40px', minHeight: '40px', maxHeight: '40px' }}
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className={`absolute right-2.5 top-2/5  transform -translate-y-1/2 text-sm ${isNightMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'}`}
              >
                {showPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className={`
                w-full py-2.5 rounded-lg font-semibold text-white transition-all duration-300 mt-3 text-sm
                bg-gradient-to-r from-purple-500 to-pink-500 hover:from-pink-500 hover:to-purple-500
                hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed
                ${isLoading ? 'animate-pulse' : ''}
              `}
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <motion.div
                    className="w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  />
                  Signing In...
                </span>
              ) : (
                '🌸 SIGN IN TO ANIMEVERSE'
              )}
            </button>
          </form>

          {/* Forgot Password Link */}
          <div className="text-center">
            <button
              onClick={() => navigate('/forgot-password')}
              className={`text-sm transition-colors ${
                isNightMode ? 'text-purple-400 hover:text-purple-300' : 'text-purple-600 hover:text-purple-700'
              }`}
            >
              Forgot Password? 🔑
            </button>
          </div>
        </div>

        {/* Right Panel - Anime Themed */}
        <div className={`
          w-full md:w-2/5 p-4 md:p-6 flex flex-col justify-center items-center text-center relative overflow-hidden
          h-32 md:h-auto rounded-b-xl md:rounded-r-xl md:rounded-bl-full
          bg-gradient-to-br from-purple-500 via-pink-500 to-blue-500
        `}
        >
          {/* Animated Background Elements - Mobile Optimized */}
          <motion.div
            className="absolute top-4 right-4 md:top-8 md:right-8 w-8 h-8 md:w-12 md:h-12 bg-white bg-opacity-15 rounded-full"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.15, 0.3, 0.15]
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />

          <motion.div
            className="absolute bottom-6 left-6 md:bottom-12 md:left-12 w-6 h-6 md:w-10 md:h-10 bg-white bg-opacity-10 rounded-full"
            animate={{
              scale: [1, 1.4, 1],
              rotate: [0, 360],
              opacity: [0.1, 0.25, 0.1]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
          />

          <motion.div
            className="absolute top-1/2 left-4 md:left-8 w-4 h-4 md:w-8 md:h-8 bg-white bg-opacity-12 rounded-full"
            animate={{
              y: [-10, 10, -10],
              opacity: [0.12, 0.2, 0.12]
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
          />

          {/* Anime Character - Luffy */}
          <motion.div
            className="absolute left-2 top-2 md:left-4 md:top-4 w-12 h-12 md:w-16 md:h-16 rounded-full overflow-hidden shadow-lg border-2 border-white border-opacity-30"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{
              opacity: 1,
              scale: 1,
              y: [0, -4, 0]
            }}
            transition={{
              duration: 1,
              y: {
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }
            }}
          >
            <img
              src={assets.luffy}
              alt="Luffy"
              className="w-full h-full object-cover"
            />
          </motion.div>

          {/* Welcome Content - Compact */}
          <div className="relative z-10 text-white flex items-center justify-between w-full md:flex-col md:text-center">
            <div className="flex items-center md:flex-col">
              <img src={assets.logo} alt="AnimeVerse" className="h-6 md:h-8 mr-3 md:mr-0 md:mb-2" />
              <div>
                <h1 className="text-lg md:text-xl font-bold">Welcome Back!</h1>
                <p className="text-xs md:text-sm opacity-90">Ready for more anime adventures! 🎌</p>
              </div>
            </div>
            
            <button
              className="bg-white bg-opacity-20 backdrop-blur-sm border border-white border-opacity-50 text-white px-3 py-1.5 md:px-4 md:py-2 rounded-full font-medium hover:bg-white hover:text-purple-600 transition-all duration-300 text-xs md:text-sm md:mt-3"
              onClick={() => navigate('/signup')}
            >
              Sign Up
            </button>
          </div>
        </div>
      </motion.div>

      <Toast
        message={toastMessage}
        type={toastMessage.includes('🎉') ? "success" : "info"}
        onClose={() => setToastMessage("")}
      />
    </div>
  );
};

export default Login;
