# Environment Configuration
NODE_ENV=development
PORT=5000

# Client URL
CLIENT_URL=http://localhost:5173

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/animeverse
DB_HOST=localhost
DB_PORT=5432
DB_NAME=animeverse
DB_USER=animeverse_user
DB_PASSWORD=your_password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# File Upload Configuration (Cloudinary)
CLOUDINARY_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379

# Email Configuration (for notifications)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12

# API Keys (for external services)
ANIME_API_KEY=your-anime-api-key
PAYMENT_STRIPE_SECRET_KEY=your-stripe-secret-key
PAYMENT_STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
