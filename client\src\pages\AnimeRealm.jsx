import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaArrowLeft, FaSearch, FaFilter, FaStar, FaFire, FaCalendar,
  FaEye, FaHeart, FaPlay, FaTags, FaSort, FaTh, FaList
} from 'react-icons/fa';
import { useTheme } from '../context/ThemeContext';
import { useNavigate } from 'react-router-dom';

const AnimeRealm = () => {
  const { isNightMode } = useTheme();
  const navigate = useNavigate();
  
  const [activeTab, setActiveTab] = useState('discover'); // discover, trending, new-releases, recommendations
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState('grid'); // grid, list
  const [sortBy, setSortBy] = useState('popularity'); // popularity, rating, year, name

  // Filter states
  const [filters, setFilters] = useState({
    genre: [],
    year: '',
    studio: '',
    status: '',
    rating: '',
    episodes: ''
  });

  // Mock anime data
  const trendingAnime = [
    {
      id: 1,
      title: 'Demon Slayer: Hashira Training Arc',
      image: '🗡️',
      rating: 9.2,
      year: 2024,
      episodes: 8,
      status: 'Airing',
      genre: ['Action', 'Supernatural'],
      studio: 'Ufotable',
      description: 'The Hashira Training Arc continues the epic journey...',
      popularity: 98,
      trending: true
    },
    {
      id: 2,
      title: 'Jujutsu Kaisen Season 3',
      image: '👻',
      rating: 9.0,
      year: 2024,
      episodes: 12,
      status: 'Coming Soon',
      genre: ['Action', 'School', 'Supernatural'],
      studio: 'MAPPA',
      description: 'The highly anticipated third season...',
      popularity: 95,
      trending: true
    },
    {
      id: 3,
      title: 'Chainsaw Man',
      image: '⚡',
      rating: 8.8,
      year: 2022,
      episodes: 12,
      status: 'Completed',
      genre: ['Action', 'Horror', 'Supernatural'],
      studio: 'MAPPA',
      description: 'Denji becomes the Chainsaw Devil...',
      popularity: 92,
      trending: false
    },
    {
      id: 4,
      title: 'Spy x Family Season 2',
      image: '🕵️',
      rating: 9.1,
      year: 2023,
      episodes: 12,
      status: 'Completed',
      genre: ['Comedy', 'Action', 'Family'],
      studio: 'Wit Studio',
      description: 'The Forger family continues their mission...',
      popularity: 90,
      trending: false
    },
    {
      id: 5,
      title: 'Attack on Titan: Final Season',
      image: '⚔️',
      rating: 9.5,
      year: 2023,
      episodes: 87,
      status: 'Completed',
      genre: ['Action', 'Drama', 'Military'],
      studio: 'MAPPA',
      description: 'The epic conclusion to humanity\'s fight...',
      popularity: 97,
      trending: false
    },
    {
      id: 6,
      title: 'One Piece',
      image: '🏴‍☠️',
      rating: 9.3,
      year: 1999,
      episodes: 1000,
      status: 'Ongoing',
      genre: ['Adventure', 'Comedy', 'Action'],
      studio: 'Toei Animation',
      description: 'Luffy\'s journey to become Pirate King...',
      popularity: 99,
      trending: true
    }
  ];

  const newReleases = [
    {
      id: 7,
      title: 'Blue Lock Season 2',
      image: '⚽',
      rating: 8.5,
      year: 2024,
      episodes: 'TBA',
      status: 'Upcoming',
      genre: ['Sports', 'Drama'],
      studio: 'Studio 8bit',
      description: 'The soccer prodigies return...',
      releaseDate: '2024-10-15'
    },
    {
      id: 8,
      title: 'Tokyo Revengers Season 4',
      image: '🏍️',
      rating: 8.3,
      year: 2024,
      episodes: 'TBA',
      status: 'Upcoming',
      genre: ['Action', 'Drama', 'Time Travel'],
      studio: 'Liden Films',
      description: 'Takemichi\'s final battle...',
      releaseDate: '2024-11-20'
    }
  ];

  const genres = [
    'Action', 'Adventure', 'Comedy', 'Drama', 'Fantasy', 'Horror', 
    'Mystery', 'Romance', 'Sci-Fi', 'Slice of Life', 'Sports', 'Supernatural'
  ];

  const studios = [
    'MAPPA', 'Ufotable', 'Wit Studio', 'Toei Animation', 'Madhouse', 
    'Studio Pierrot', 'Bones', 'A-1 Pictures', 'Trigger', 'Kyoto Animation'
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'Airing': return 'text-green-500';
      case 'Completed': return 'text-blue-500';
      case 'Upcoming': return 'text-purple-500';
      case 'Coming Soon': return 'text-orange-500';
      case 'Ongoing': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const filteredAnime = trendingAnime.filter(anime => {
    if (searchQuery && !anime.title.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    if (filters.genre.length > 0 && !filters.genre.some(g => anime.genre.includes(g))) {
      return false;
    }
    if (filters.year && anime.year.toString() !== filters.year) {
      return false;
    }
    if (filters.studio && anime.studio !== filters.studio) {
      return false;
    }
    if (filters.status && anime.status !== filters.status) {
      return false;
    }
    return true;
  });

  const sortedAnime = [...filteredAnime].sort((a, b) => {
    switch (sortBy) {
      case 'rating': return b.rating - a.rating;
      case 'year': return b.year - a.year;
      case 'name': return a.title.localeCompare(b.title);
      case 'popularity': 
      default: return b.popularity - a.popularity;
    }
  });

  return (
    <div className={`min-h-screen overflow-hidden transition-all duration-300 ${
      isNightMode 
        ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900' 
        : 'bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50'
    }`}>
      <div className="h-screen overflow-y-auto hide-scrollbar">
        
        {/* Floating Sakura Petals */}
        <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-pink-300 rounded-full opacity-20"
              style={{
                left: `${Math.random() * 100}%`,
                top: `-10px`
              }}
              animate={{
                y: window.innerHeight + 50,
                x: [0, 30, -30, 0],
                rotate: 360,
                opacity: [0.2, 0.05, 0.2]
              }}
              transition={{
                duration: Math.random() * 3 + 12,
                repeat: Infinity,
                delay: Math.random() * 5,
                ease: "linear"
              }}
            />
          ))}
        </div>

        <div className="relative z-10 max-w-7xl mx-auto p-4">
          
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={() => navigate('/home')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                isNightMode ? 'text-gray-300 hover:text-white hover:bg-gray-700' : 'text-gray-600 hover:text-gray-800 hover:bg-white'
              }`}
            >
              <FaArrowLeft className="w-4 h-4" />
              <span>Back to Home</span>
            </button>
            
            <h1 className={`text-3xl font-bold flex items-center space-x-3 ${
              isNightMode ? 'text-white' : 'text-gray-800'
            }`}>
              <span>🔍</span>
              <span>Anime Realm</span>
            </h1>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className={`p-2 rounded-lg transition-colors ${
                  isNightMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {viewMode === 'grid' ? <FaList className="w-5 h-5" /> : <FaTh className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {/* Search and Filters */}
          <div className={`p-6 rounded-2xl mb-6 backdrop-blur-sm ${
            isNightMode ? 'bg-gray-800/90' : 'bg-white/90'
          }`}>
            <div className="flex flex-col md:flex-row gap-4">
              
              {/* Search Bar */}
              <div className="flex-1 relative">
                <FaSearch className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${
                  isNightMode ? 'text-gray-400' : 'text-gray-500'
                }`} />
                <input
                  type="text"
                  placeholder="Search anime titles, genres, studios..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-colors ${
                    isNightMode
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500'
                      : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500'
                  } focus:outline-none focus:ring-0`}
                />
              </div>

              {/* Sort Dropdown */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className={`px-4 py-3 rounded-lg border transition-colors ${
                  isNightMode
                    ? 'bg-gray-700 border-gray-600 text-white focus:border-purple-500'
                    : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-purple-500'
                } focus:outline-none focus:ring-0`}
              >
                <option value="popularity">Sort by Popularity</option>
                <option value="rating">Sort by Rating</option>
                <option value="year">Sort by Year</option>
                <option value="name">Sort by Name</option>
              </select>

              {/* Filter Button */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center space-x-2 px-6 py-3 rounded-lg transition-colors ${
                  showFilters
                    ? 'bg-purple-500 text-white'
                    : isNightMode 
                      ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <FaFilter className="w-4 h-4" />
                <span>Filters</span>
              </button>
            </div>

            {/* Advanced Filters */}
            <AnimatePresence>
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="mt-6 pt-6 border-t border-gray-300"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    
                    {/* Genre Filter */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${
                        isNightMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Genres
                      </label>
                      <div className="max-h-32 overflow-y-auto hide-scrollbar space-y-1">
                        {genres.map(genre => (
                          <label key={genre} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={filters.genre.includes(genre)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setFilters({...filters, genre: [...filters.genre, genre]});
                                } else {
                                  setFilters({...filters, genre: filters.genre.filter(g => g !== genre)});
                                }
                              }}
                              className="rounded text-purple-500 focus:ring-purple-500"
                            />
                            <span className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-700'}`}>
                              {genre}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>

                    {/* Year Filter */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${
                        isNightMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Year
                      </label>
                      <input
                        type="number"
                        placeholder="e.g. 2024"
                        value={filters.year}
                        onChange={(e) => setFilters({...filters, year: e.target.value})}
                        className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                          isNightMode
                            ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                            : 'bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500'
                        } focus:outline-none focus:ring-0 focus:border-purple-500`}
                      />
                    </div>

                    {/* Studio Filter */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${
                        isNightMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Studio
                      </label>
                      <select
                        value={filters.studio}
                        onChange={(e) => setFilters({...filters, studio: e.target.value})}
                        className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                          isNightMode
                            ? 'bg-gray-700 border-gray-600 text-white'
                            : 'bg-gray-50 border-gray-300 text-gray-900'
                        } focus:outline-none focus:ring-0 focus:border-purple-500`}
                      >
                        <option value="">All Studios</option>
                        {studios.map(studio => (
                          <option key={studio} value={studio}>{studio}</option>
                        ))}
                      </select>
                    </div>

                    {/* Status Filter */}
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${
                        isNightMode ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Status
                      </label>
                      <select
                        value={filters.status}
                        onChange={(e) => setFilters({...filters, status: e.target.value})}
                        className={`w-full px-3 py-2 rounded-lg border transition-colors ${
                          isNightMode
                            ? 'bg-gray-700 border-gray-600 text-white'
                            : 'bg-gray-50 border-gray-300 text-gray-900'
                        } focus:outline-none focus:ring-0 focus:border-purple-500`}
                      >
                        <option value="">All Status</option>
                        <option value="Airing">Airing</option>
                        <option value="Completed">Completed</option>
                        <option value="Upcoming">Upcoming</option>
                        <option value="Coming Soon">Coming Soon</option>
                        <option value="Ongoing">Ongoing</option>
                      </select>
                    </div>
                  </div>

                  {/* Clear Filters */}
                  <div className="mt-4 flex justify-end">
                    <button
                      onClick={() => setFilters({
                        genre: [],
                        year: '',
                        studio: '',
                        status: '',
                        rating: '',
                        episodes: ''
                      })}
                      className={`px-4 py-2 rounded-lg text-sm transition-colors ${
                        isNightMode 
                          ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700' 
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      Clear All Filters
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Navigation Tabs */}
          <div className={`flex space-x-1 mb-6 p-1 rounded-xl ${
            isNightMode ? 'bg-gray-800/50' : 'bg-white/50'
          }`}>
            {[
              { id: 'discover', label: 'Discover', icon: FaSearch },
              { id: 'trending', label: 'Trending', icon: FaFire },
              { id: 'new-releases', label: 'New Releases', icon: FaCalendar },
              { id: 'recommendations', label: 'For You', icon: FaHeart }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                    : isNightMode 
                      ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700/50' 
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span className="font-medium">{tab.label}</span>
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <AnimatePresence mode="wait">
            {activeTab === 'discover' && (
              <motion.div
                key="discover"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <AnimeGrid anime={sortedAnime} viewMode={viewMode} isNightMode={isNightMode} />
              </motion.div>
            )}

            {activeTab === 'trending' && (
              <motion.div
                key="trending"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="mb-6">
                  <h2 className={`text-2xl font-bold flex items-center space-x-2 ${
                    isNightMode ? 'text-white' : 'text-gray-800'
                  }`}>
                    <FaFire className="text-red-500" />
                    <span>Trending Now</span>
                  </h2>
                  <p className={`text-sm mt-1 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    Most popular anime this week
                  </p>
                </div>
                <AnimeGrid
                  anime={trendingAnime.filter(a => a.trending)}
                  viewMode={viewMode}
                  isNightMode={isNightMode}
                />
              </motion.div>
            )}

            {activeTab === 'new-releases' && (
              <motion.div
                key="new-releases"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="mb-6">
                  <h2 className={`text-2xl font-bold flex items-center space-x-2 ${
                    isNightMode ? 'text-white' : 'text-gray-800'
                  }`}>
                    <FaCalendar className="text-green-500" />
                    <span>New Releases</span>
                  </h2>
                  <p className={`text-sm mt-1 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    Upcoming and recently released anime
                  </p>
                </div>
                <AnimeGrid anime={newReleases} viewMode={viewMode} isNightMode={isNightMode} />
              </motion.div>
            )}

            {activeTab === 'recommendations' && (
              <motion.div
                key="recommendations"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="mb-6">
                  <h2 className={`text-2xl font-bold flex items-center space-x-2 ${
                    isNightMode ? 'text-white' : 'text-gray-800'
                  }`}>
                    <FaHeart className="text-pink-500" />
                    <span>Recommended for You</span>
                  </h2>
                  <p className={`text-sm mt-1 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    Based on your Naruto clan preferences and viewing history
                  </p>
                </div>
                <AnimeGrid
                  anime={trendingAnime.filter(a => a.genre.includes('Action') || a.genre.includes('Adventure'))}
                  viewMode={viewMode}
                  isNightMode={isNightMode}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

// Anime Grid Component
const AnimeGrid = ({ anime, viewMode, isNightMode }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'Airing': return 'text-green-500';
      case 'Completed': return 'text-blue-500';
      case 'Upcoming': return 'text-purple-500';
      case 'Coming Soon': return 'text-orange-500';
      case 'Ongoing': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  if (viewMode === 'list') {
    return (
      <div className="space-y-4">
        {anime.map((item, index) => (
          <motion.div
            key={item.id}
            className={`p-6 rounded-xl backdrop-blur-sm transition-all hover:scale-[1.02] cursor-pointer ${
              isNightMode ? 'bg-gray-800/90 hover:bg-gray-700/90' : 'bg-white/90 hover:bg-gray-50/90'
            }`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <div className="flex items-start space-x-6">
              <div className="w-20 h-28 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-3xl shadow-lg">
                {item.image}
              </div>

              <div className="flex-1">
                <div className="flex items-start justify-between mb-2">
                  <h3 className={`text-xl font-bold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                    {item.title}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <FaStar className="text-yellow-500 w-4 h-4" />
                    <span className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                      {item.rating}
                    </span>
                  </div>
                </div>

                <div className="flex items-center space-x-4 mb-3">
                  <span className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    {item.year}
                  </span>
                  <span className={`text-sm ${getStatusColor(item.status)}`}>
                    {item.status}
                  </span>
                  <span className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    {typeof item.episodes === 'number' ? `${item.episodes} episodes` : item.episodes}
                  </span>
                  <span className={`text-sm ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    {item.studio}
                  </span>
                </div>

                <div className="flex flex-wrap gap-2 mb-3">
                  {item.genre.map(g => (
                    <span
                      key={g}
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        isNightMode ? 'bg-purple-500/20 text-purple-400' : 'bg-purple-100 text-purple-700'
                      }`}
                    >
                      {g}
                    </span>
                  ))}
                </div>

                <p className={`text-sm ${isNightMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  {item.description}
                </p>

                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center space-x-4">
                    <button className="flex items-center space-x-1 text-purple-500 hover:text-purple-400 transition-colors">
                      <FaPlay className="w-4 h-4" />
                      <span className="text-sm font-medium">Watch</span>
                    </button>
                    <button className="flex items-center space-x-1 text-pink-500 hover:text-pink-400 transition-colors">
                      <FaHeart className="w-4 h-4" />
                      <span className="text-sm font-medium">Add to List</span>
                    </button>
                  </div>

                  {item.trending && (
                    <div className="flex items-center space-x-1 text-red-500">
                      <FaFire className="w-4 h-4" />
                      <span className="text-sm font-medium">Trending</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    );
  }

  // Grid View
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {anime.map((item, index) => (
        <motion.div
          key={item.id}
          className={`p-4 rounded-xl backdrop-blur-sm transition-all hover:scale-105 cursor-pointer ${
            isNightMode ? 'bg-gray-800/90 hover:bg-gray-700/90' : 'bg-white/90 hover:bg-gray-50/90'
          }`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <div className="relative mb-4">
            <div className="w-full h-48 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-6xl shadow-lg">
              {item.image}
            </div>
            {item.trending && (
              <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center space-x-1">
                <FaFire className="w-3 h-3" />
                <span>HOT</span>
              </div>
            )}
          </div>

          <h3 className={`font-bold text-lg mb-2 line-clamp-2 ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
            {item.title}
          </h3>

          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-1">
              <FaStar className="text-yellow-500 w-4 h-4" />
              <span className={`font-semibold ${isNightMode ? 'text-white' : 'text-gray-800'}`}>
                {item.rating}
              </span>
            </div>
            <span className={`text-sm ${getStatusColor(item.status)}`}>
              {item.status}
            </span>
          </div>

          <div className="flex flex-wrap gap-1 mb-3">
            {item.genre.slice(0, 2).map(g => (
              <span
                key={g}
                className={`px-2 py-1 rounded-full text-xs font-medium ${
                  isNightMode ? 'bg-purple-500/20 text-purple-400' : 'bg-purple-100 text-purple-700'
                }`}
              >
                {g}
              </span>
            ))}
          </div>

          <div className={`text-sm mb-3 ${isNightMode ? 'text-gray-400' : 'text-gray-500'}`}>
            {item.year} • {typeof item.episodes === 'number' ? `${item.episodes} eps` : item.episodes}
          </div>

          <div className="flex items-center space-x-2">
            <button className="flex-1 bg-purple-500 text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-purple-600 transition-colors flex items-center justify-center space-x-1">
              <FaPlay className="w-3 h-3" />
              <span>Watch</span>
            </button>
            <button className="p-2 border border-pink-500 text-pink-500 rounded-lg hover:bg-pink-500 hover:text-white transition-colors">
              <FaHeart className="w-4 h-4" />
            </button>
          </div>
        </motion.div>
      ))}
    </div>
  );
};

export default AnimeRealm;
