import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from './context/ThemeContext';
import Signup from './pages/auth/Signup';
import Login from './pages/auth/Login';
import ForgotPassword from './pages/auth/ForgotPassword';
import Welcome from './pages/Welcome';
import Home from './pages/Home';
import Profile from './pages/Profile';
import Clan from './pages/Clan';
import AnimeRealm from './pages/AnimeRealm';
import EdenMarket from './pages/EdenMarket';
import Festivals from './pages/Festivals';
import SenseiChat from './pages/SenseiChat';
import SpiritBell from './pages/SpiritBell';
import Settings from './pages/Settings';


function App() {
  return (
    <ThemeProvider>
      <Router>
        <div className="App">
          <Routes>
            <Route path="/" element={<Navigate to="/signup" replace />} />
            <Route path="/signup" element={<Signup />} />
            <Route path="/login" element={<Login />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/welcome" element={<Welcome />} />
            <Route path="/home" element={<Home />} />
            <Route path="/profile" element={<Profile />} />
            <Route path="/clan" element={<Clan />} />
            <Route path="/anime-realm" element={<AnimeRealm />} />
            <Route path="/eden-market" element={<EdenMarket />} />
            <Route path="/festivals" element={<Festivals />} />
            <Route path="/sensei-chat" element={<SenseiChat />} />
            <Route path="/spirit-bell" element={<SpiritBell />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </div>
      </Router>
    </ThemeProvider>
  );
}

export default App;
